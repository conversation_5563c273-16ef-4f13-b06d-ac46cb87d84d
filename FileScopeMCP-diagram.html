<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>File Scope Diagram - core-structure</title>
  <!-- Load Mermaid from CDN -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: background 0.5s ease;
    }
    .dark-mode {
      background: linear-gradient(135deg, #1e1e2f 0%, #1d2426 100%);
    }
    .light-mode {
      background: linear-gradient(135deg, #f5f6fa 0%, #dcdde1 100%);
    }
    header {
      position: absolute;
      top: 20px;
      left: 20px;
      text-align: left;
    }
    #theme-toggle {
      position: absolute;
      top: 20px;
      right: 20px;
      padding: 10px 20px;
      border: none;
      border-radius: 50px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    #diagram-container {
      width: 90%;
      max-width: 1200px;
      margin: 75px 0;
      padding: 25px;
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
      transition: all 0.5s ease;
      position: relative;
    }
    #mermaid-graph {
      overflow: auto;
      max-height: 70vh;
    }
    #error-message {
      position: absolute;
      bottom: 10px;
      left: 10px;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      display: none;
    }
  </style>
</head>
<body class="dark-mode">
  <!-- Header -->
  <header style="color: #ffffff;">
    <h1 style="margin: 0; font-size: 28px;">File Scope Diagram - core-structure</h1>
    <div style="font-size: 14px; margin-top: 5px;">Generated on Sun Mar 30 2025 11:02:14 AM</div>
  </header>

  <!-- Theme Toggle Button -->
  <button id="theme-toggle" style="background: #2d3436; color: #ffffff;">Switch to Light Mode</button>

  <!-- Diagram Container -->
  <div id="diagram-container" style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1);">
    <div id="mermaid-graph"></div>
    <div id="error-message" style="background: rgba(45, 52, 54, 0.9); color: #ff7675;"></div>
    <!-- Mermaid Code -->
    <pre id="raw-code" style="display: none;">
graph LR
node0[FileScopeMCP]
style node0 fill:#74b9ff,stroke:#2d3436
node1[.cursor]
style node1 fill:#74b9ff,stroke:#2d3436
node2[diagrams]
style node2 fill:#74b9ff,stroke:#2d3436
node3[dist]
style node3 fill:#74b9ff,stroke:#2d3436
node4[components]
style node4 fill:#74b9ff,stroke:#2d3436
node5[mcp-server.js]
style node5 fill:#ff7675,stroke:#2d3436
node6[file-utils.js]
style node6 fill:#81ecec,stroke:#2d3436
node7[storage-utils.js]
style node7 fill:#81ecec,stroke:#2d3436
node8[mermaid-generator.js]
style node8 fill:#81ecec,stroke:#2d3436
node9[package.json]
style node9 fill:#74b9ff,stroke:#2d3436
node11[file-utils.ts]
style node11 fill:#ff7675,stroke:#2d3436
node12[types.ts]
style node12 fill:#ff7675,stroke:#2d3436
node13[storage-utils.ts]
style node13 fill:#ff7675,stroke:#2d3436
node10[src]
style node10 fill:#74b9ff,stroke:#2d3436
node14[mcp-server.ts]
style node14 fill:#ff7675,stroke:#2d3436
node15[mermaid-generator.ts]
style node15 fill:#ff7675,stroke:#2d3436
node0 --> node1
linkStyle 0 stroke:#dfe4ea
node0 --> node2
linkStyle 1 stroke:#dfe4ea
node3 --> node4
linkStyle 2 stroke:#dfe4ea
node5 --> node6
linkStyle 3 stroke:#636e72
node5 --> node7
linkStyle 4 stroke:#636e72
node5 --> node8
linkStyle 5 stroke:#636e72
node3 --> node5
linkStyle 6 stroke:#dfe4ea
node0 --> node3
linkStyle 7 stroke:#dfe4ea
node0 --> node9
linkStyle 8 stroke:#dfe4ea
node11 --> node12
linkStyle 9 stroke:#636e72
node11 --> node13
linkStyle 10 stroke:#636e72
node10 --> node11
linkStyle 11 stroke:#dfe4ea
node14 --> node12
linkStyle 12 stroke:#636e72
node14 --> node11
linkStyle 13 stroke:#636e72
node14 --> node13
linkStyle 14 stroke:#636e72
node14 --> node15
linkStyle 15 stroke:#636e72
node10 --> node14
linkStyle 16 stroke:#dfe4ea
node15 --> node12
linkStyle 17 stroke:#636e72
node10 --> node15
linkStyle 18 stroke:#dfe4ea
node13 --> node12
linkStyle 19 stroke:#636e72
node10 --> node13
linkStyle 20 stroke:#dfe4ea
node10 --> node12
linkStyle 21 stroke:#dfe4ea
node0 --> node10
linkStyle 22 stroke:#dfe4ea
    </pre>
  </div>

  <script>
    // Unique render ID counter
    let renderCount = 0;

    // Initialize Mermaid with dark theme by default
    mermaid.initialize({
      startOnLoad: false,
      theme: 'dark',
      securityLevel: 'loose',
      flowchart: {
        htmlLabels: true,
        curve: 'basis',
        nodeSpacing: 42,
        rankSpacing: 60,
        useMaxWidth: true
      },
      themeVariables: {
        // Make node text bright white in dark mode for better readability
        nodeBorder: "#2d3436",
        mainBkg: "#1e272e",
        nodeTextColor: "#ffffff", 
        fontSize: "16px"
      }
    });

    // Render on DOM load
    document.addEventListener('DOMContentLoaded', () => {
      if (typeof mermaid === 'undefined') {
        console.error('Mermaid library failed to load. Check network or CDN URL.');
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('error-message').textContent = 'Error: Mermaid library not loaded';
        return;
      }
      renderMermaid();
    });

    // Render Mermaid diagram
    function renderMermaid() {
      const mermaidDiv = document.getElementById('mermaid-graph');
      const errorDiv = document.getElementById('error-message');
      const rawCode = document.getElementById('raw-code').textContent.trim();
      const uniqueId = `mermaid-svg-${Date.now()}-${renderCount++}`;

      // Clear previous content
      mermaidDiv.innerHTML = '';
      errorDiv.style.display = 'none';

      // Render using promise
      mermaid.render(uniqueId, rawCode)
        .then(({ svg }) => {
          mermaidDiv.innerHTML = svg;
        })
        .catch(error => {
          console.error('Mermaid rendering failed:', error);
          errorDiv.style.display = 'block';
          errorDiv.textContent = `Error: ${error.message}`;
          mermaidDiv.innerHTML = `<pre style="color: #ff7675;">${rawCode}</pre>`;
        });
    }

    // Theme toggle function
    function toggleTheme() {
      const body = document.body;
      const toggleBtn = document.getElementById('theme-toggle');
      const diagramContainer = document.getElementById('diagram-container');
      const header = document.querySelector('header');
      const isDarkMode = body.classList.contains('dark-mode');

      if (isDarkMode) {
        // Switch to Light Mode
        body.classList.remove('dark-mode');
        body.classList.add('light-mode');
        toggleBtn.textContent = 'Switch to Dark Mode';
        toggleBtn.style.background = '#dcdde1';
        toggleBtn.style.color = '#2d3436';
        diagramContainer.style.background = 'rgba(255, 255, 255, 0.8)';
        diagramContainer.style.border = '1px solid rgba(0, 0, 0, 0.1)';
        header.style.color = '#2d3436';
        
        // Update Mermaid theme to light with dark text
        mermaid.initialize({
          theme: 'default',
          themeVariables: {
            nodeBorder: "#2d3436",
            mainBkg: "#f8f9fa",
            nodeTextColor: "#333333",
            fontSize: "16px"
          }
        });
      } else {
        // Switch to Dark Mode
        body.classList.remove('light-mode');
        body.classList.add('dark-mode');
        toggleBtn.textContent = 'Switch to Light Mode';
        toggleBtn.style.background = '#2d3436';
        toggleBtn.style.color = '#ffffff';
        diagramContainer.style.background = 'rgba(255, 255, 255, 0.05)';
        diagramContainer.style.border = '1px solid rgba(255, 255, 255, 0.1)';
        header.style.color = '#ffffff';
        
        // Update Mermaid theme to dark with bright white text
        mermaid.initialize({
          theme: 'dark',
          themeVariables: {
            nodeBorder: "#2d3436",
            mainBkg: "#1e272e",
            nodeTextColor: "#ffffff",
            fontSize: "16px"
          }
        });
      }

      // Re-render diagram after theme change
      renderMermaid();
    }

    // Attach theme toggle event
    document.getElementById('theme-toggle').addEventListener('click', toggleTheme);
  </script>
</body>
</html>