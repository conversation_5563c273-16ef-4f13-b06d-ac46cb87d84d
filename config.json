{"excludePatterns": ["**/node_modules", "**/.git", "**/dist", "**/build", "**/coverage", "logs", "**/*.log", "**/*.lock", "**/*.bak", "**/*.tmp", "**/*.temp", "**/.DS_Store", "**/.vscode", "**/.idea", "**/Thumbs.db", "**/.cursorrules", "**/.cursor", "**/package-lock.json", "**/.env*", "**/*.gradle", "**/*-tree.json", "**/*-excludes.json", "**/*.pyc", "**/*.pyo", "**/__pycache__", "**/*.Zone.Identifier", "**/.yarn", "**/target", "**/*.o", "**/*.obj", "**/*.so", "**/*.out", "**/obj/**", "**/*.rlib", "**/zig-cache", "**/zig-out", "**/*.js.map", "**/*.ts.map", "**/venv", "**/venv_*", "**/.venv", "**/.venv_*", "**/.tox", "**.pytest_cache", "**.eggs", "**.eggs-info", "**/.ipynb_checkpoints", "**/jspm_packages", "**/.next", "**/.nuxt", "**/.angular", "**/.expo", "**/.parcel-cache", "**/.turbo", "**/.vercel", "**/.firebase", "**/.output", "**/.svelte-kit", "**/.storybook", "**/cache", "**/.cache", "**/.cache-loader", "**/.npm", "**/.pnpm-store", "**/cmake-build-*", "**/CMakeFiles", "**/<PERSON><PERSON><PERSON>", "**/CMakeCache.txt", "**/*.a", "**/.cargo", "**/Cargo.lock", "**/.rustup", "**/.swp", "**/.local", "**/*.gradle"], "fileWatching": {"enabled": true, "debounceMs": 100, "ignoreDotFiles": true, "autoRebuildTree": true, "maxWatchedDirectories": 500, "watchForNewFiles": true, "watchForDeleted": true, "watchForChanged": true}, "baseDirectory": "/home/<USER>/festive", "version": "2.6.0"}