
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for file-utils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> file-utils.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.57% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>64/845</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">92.85% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>13/14</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.53% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>3/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.57% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>64/845</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">27x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">16x</span>
<span class="cline-any cline-yes">16x</span>
<span class="cline-any cline-yes">16x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import * as fs from 'fs';
import * as fsPromises from 'fs/promises';
import * as path from 'path';
import * as fsSync from "fs";
import { FileNode, PackageDependency, FileTreeConfig } from "./types.js";
import { normalizeAndResolvePath } from "./storage-utils.js";
import { getProjectRoot, getConfig, addExclusionPattern } from './global-state.js';
import { saveFileTree } from './storage-utils.js'; // Import saveFileTree
import { log } from './logger.js'; // Import the logger
&nbsp;
/**
 * Normalizes a file path for consistent comparison across platforms
 * Handles Windows and Unix paths, relative and absolute paths
 */
export function normalizePath(filepath: string): string {
  if (!filepath) return '';
  
  try {
    // Handle URL-encoded paths
    const decoded = filepath.includes('%') ? decodeURIComponent(filepath) : filepath;
    
    // Handle Windows paths with drive letters that may start with a slash
    const cleanPath = decoded.match(/^\/[a-zA-Z]:/) ? decoded.substring(1) : decoded;
    
    // Handle Windows backslashes by converting to forward slashes
    // Note: we need to escape the backslash in regex since it's a special character
    const forwardSlashed = cleanPath.replace(/\\/g, '/');
    
    // Remove any double quotes that might be present
    const noQuotes = forwardSlashed.replace(/"/g, '');
    
    // Remove duplicate slashes
    const deduped = noQuotes.replace(/\/+/g, '/');
    
    // Remove trailing slash
    return deduped.endsWith('/') ? deduped.slice(0, -1) : deduped;
<span class="branch-0 cbranch-no" title="branch not covered" >  } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >    log(`Failed to normalize path: ${filepath} - ${error}`);</span>
    // Return original as fallback
<span class="cstat-no" title="statement not covered" >    return filepath;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
}
&nbsp;
export function toPlatformPath(normalizedPath: string): string {
  return normalizedPath.split('/').join(path.sep);
}
&nbsp;
const SUPPORTED_EXTENSIONS = [
  ".py", ".c", ".cpp", ".h", ".rs", ".lua", ".js", ".jsx", ".ts",
  ".tsx", ".zig", ".php", ".blade.php", ".phtml", ".cs", ".java" ];
&nbsp;
const IMPORT_PATTERNS: { [key: string]: RegExp } = {
  '.js': /(?:import\s+(?:(?:[\w*\s{},]*)\s+from\s+)?["']([^"']+)["'])|(?:require\(["']([^"']+)["']\))|(?:import\s*\(["']([^"']+)["']\))/g,
  '.jsx': /(?:import\s+(?:[^;]*?)\s+from\s+["']([^"']+)["'])|(?:import\s+["']([^"']+)["'])|(?:require\(["']([^"']+)["']\))|(?:import\s*\(["']([^"']+)["']\))/g,
  '.ts': /(?:import\s+(?:(?:[\w*\s{},]*)\s+from\s+)?["']([^"']+)["'])|(?:require\(["']([^"']+)["']\))|(?:import\s*\(["']([^"']+)["']\))/g,
  '.tsx': /(?:import\s+(?:[^;]*?)\s+from\s+["']([^"']+)["'])|(?:import\s+["']([^"']+)["'])|(?:require\(["']([^"']+)["']\))|(?:import\s*\(["']([^"']+)["']\))/g,
  '.py': /(?:import\s+[\w.]+|from\s+[\w.]+\s+import\s+[\w*]+)/g,
  '.c': /#include\s+["&lt;][^"&gt;]+["&gt;]/g,
  '.cpp': /#include\s+["&lt;][^"&gt;]+["&gt;]/g,
  '.h': /#include\s+["&lt;][^"&gt;]+["&gt;]/g,
  '.rs': /use\s+[\w:]+|mod\s+\w+/g,
  '.lua': /require\s*\(['"][^'"]+['"]\)/g,
  '.zig': /@import\s*\(['"][^'"]+['"]\)|const\s+[\w\s,{}]+\s*=\s*@import\s*\(['"][^'"]+['"]\)/g,
  '.php': /(?:(?:require|require_once|include|include_once)\s*\(?["']([^"']+)["']\)?)|(?:use\s+([A-Za-z0-9\\]+(?:\s+as\s+[A-Za-z0-9]+)?);)/g,
  '.blade.php': /@(?:include|extends|component)\s*\(\s*["']([^"']+)["']\s*\)|@(?:include|extends|component)\s*\(\s*["']([^"']+)["']\s*,\s*\[.*?\]\s*\)|@(?:include|extends|component)\s*\(["']([^"']+)["']\)/g,
  '.phtml': /(?:(?:require|require_once|include|include_once)\s*\(?["']([^"']+)["']\)?)|(?:use\s+([A-Za-z0-9\\]+(?:\s+as\s+[A-Za-z0-9]+)?);)/g,
  '.cs': /using\s+[\w.]+;/g,
  '.java': /import\s+[\w.]+;/g
};
&nbsp;
/**
 * Utility function to detect unresolved template literals in strings
 * This helps prevent treating template literals like ${importPath} as actual import paths
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function isUnresolvedTemplateLiteral(str: string): boolean {</span></span>
  // Check for ${...} pattern which indicates an unresolved template literal
<span class="cstat-no" title="statement not covered" >  return typeof str === 'string' &amp;&amp; </span>
<span class="cstat-no" title="statement not covered" >         str.includes('${') &amp;&amp; </span>
<span class="cstat-no" title="statement not covered" >         str.includes('}');</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper to resolve TypeScript/JavaScript import paths
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function resolveImportPath(importPath: string, currentFilePath: string, baseDir: string): string {</span></span>
<span class="cstat-no" title="statement not covered" >  log(`Resolving import path: ${importPath} from file: ${currentFilePath}`);</span>
  
  // Check if the importPath is an unresolved template literal
<span class="cstat-no" title="statement not covered" >  if (isUnresolvedTemplateLiteral(importPath)) {</span>
<span class="cstat-no" title="statement not covered" >    log(`Warning: Attempting to resolve unresolved template literal: ${importPath}`);</span>
    // We'll return a special path that's unlikely to exist or cause issues
<span class="cstat-no" title="statement not covered" >    return path.join(baseDir, '_UNRESOLVED_TEMPLATE_PATH_');</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // For TypeScript files, if the import ends with .js, convert it to .ts
<span class="cstat-no" title="statement not covered" >  if (currentFilePath.endsWith('.ts') || currentFilePath.endsWith('.tsx')) {</span>
<span class="cstat-no" title="statement not covered" >    if (importPath.endsWith('.js')) {</span>
<span class="cstat-no" title="statement not covered" >      importPath = importPath.replace(/\.js$/, '.ts');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Handle relative imports
<span class="cstat-no" title="statement not covered" >  if (importPath.startsWith('.')) {</span>
<span class="cstat-no" title="statement not covered" >    const resolvedPath = path.resolve(path.dirname(currentFilePath), importPath);</span>
<span class="cstat-no" title="statement not covered" >    log(`Resolved relative import to: ${resolvedPath}`);</span>
<span class="cstat-no" title="statement not covered" >    return path.normalize(resolvedPath);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle absolute imports (from project root)
<span class="cstat-no" title="statement not covered" >  if (importPath.startsWith('/')) {</span>
<span class="cstat-no" title="statement not covered" >    const resolvedPath = path.join(baseDir, importPath);</span>
<span class="cstat-no" title="statement not covered" >    log(`Resolved absolute import to: ${resolvedPath}`);</span>
<span class="cstat-no" title="statement not covered" >    return path.normalize(resolvedPath);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Handle package imports
<span class="cstat-no" title="statement not covered" >  const nodeModulesPath = path.join(baseDir, 'node_modules', importPath);</span>
<span class="cstat-no" title="statement not covered" >  log(`Resolved package import to: ${nodeModulesPath}`);</span>
<span class="cstat-no" title="statement not covered" >  return path.normalize(nodeModulesPath);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function calculateInitialImportance(filePath: string, baseDir: string): number {</span></span>
<span class="cstat-no" title="statement not covered" >  let importance = 0;</span>
<span class="cstat-no" title="statement not covered" >  const ext = path.extname(filePath);</span>
<span class="cstat-no" title="statement not covered" >  const relativePath = path.relative(baseDir, filePath);</span>
<span class="cstat-no" title="statement not covered" >  const parts = relativePath.split(path.sep);</span>
<span class="cstat-no" title="statement not covered" >  const fileName = path.basename(filePath, ext);</span>
&nbsp;
  // Base importance by file type
<span class="cstat-no" title="statement not covered" >  switch (ext) {</span>
<span class="cstat-no" title="statement not covered" >    case '.ts':</span>
<span class="cstat-no" title="statement not covered" >    case '.tsx':</span>
<span class="cstat-no" title="statement not covered" >      importance += 3;</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    case '.js':</span>
<span class="cstat-no" title="statement not covered" >    case '.jsx':</span>
<span class="cstat-no" title="statement not covered" >      importance += 2;</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    case '.php':</span>
      // PHP controllers and models are highly important
<span class="cstat-no" title="statement not covered" >      if (fileName.toLowerCase().includes('controller') || fileName.toLowerCase().includes('model')) {</span>
<span class="cstat-no" title="statement not covered" >        importance += 3;</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        importance += 2;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    case '.blade.php':</span>
      // Blade layout files are more important than regular views
<span class="cstat-no" title="statement not covered" >      if (fileName.toLowerCase().includes('layout') || fileName.toLowerCase().includes('app')) {</span>
<span class="cstat-no" title="statement not covered" >        importance += 3;</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        importance += 2;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    case '.json':</span>
<span class="cstat-no" title="statement not covered" >      if (fileName === 'package' || fileName === 'tsconfig' || fileName === 'composer') {</span>
<span class="cstat-no" title="statement not covered" >        importance += 3;</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        importance += 1;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    case '.md':</span>
<span class="cstat-no" title="statement not covered" >      if (fileName.toLowerCase() === 'readme') {</span>
<span class="cstat-no" title="statement not covered" >        importance += 2;</span>
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >        importance += 1;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      break;</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      importance += 0;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Importance by location
<span class="cstat-no" title="statement not covered" >  if (parts[0] === 'src' || parts[0] === 'app') {</span>
<span class="cstat-no" title="statement not covered" >    importance += 2;</span>
<span class="cstat-no" title="statement not covered" >  } else if (parts[0] === 'test' || parts[0] === 'tests') {</span>
<span class="cstat-no" title="statement not covered" >    importance += 1;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Laravel-specific directory importance
<span class="cstat-no" title="statement not covered" >  if (parts.includes('app')) {</span>
<span class="cstat-no" title="statement not covered" >    if (parts.includes('Http') &amp;&amp; parts.includes('Controllers')) {</span>
<span class="cstat-no" title="statement not covered" >      importance += 2;</span>
<span class="cstat-no" title="statement not covered" >    } else if (parts.includes('Models')) {</span>
<span class="cstat-no" title="statement not covered" >      importance += 2;</span>
<span class="cstat-no" title="statement not covered" >    } else if (parts.includes('Providers')) {</span>
<span class="cstat-no" title="statement not covered" >      importance += 2;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Importance by name significance
<span class="cstat-no" title="statement not covered" >  const significantNames = [</span>
<span class="cstat-no" title="statement not covered" >    'index', 'main', 'server', 'app', 'config', 'types', 'utils',</span>
<span class="cstat-no" title="statement not covered" >    'kernel', 'provider', 'middleware', 'service', 'repository',</span>
<span class="cstat-no" title="statement not covered" >    'controller', 'model', 'layout', 'master'</span>
<span class="cstat-no" title="statement not covered" >  ];</span>
<span class="cstat-no" title="statement not covered" >  if (significantNames.includes(fileName.toLowerCase())) {</span>
<span class="cstat-no" title="statement not covered" >    importance += 2;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Cap importance at 10
<span class="cstat-no" title="statement not covered" >  return Math.min(importance, 10);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper to extract import path from different import styles
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function extractImportPath(importStatement: string): string | null {</span></span>
  // Try to match dynamic imports first
<span class="cstat-no" title="statement not covered" >  const dynamicMatch = importStatement.match(/import\s*\(["']([^"']+)["']\)/);</span>
<span class="cstat-no" title="statement not covered" >  if (dynamicMatch) {</span>
<span class="cstat-no" title="statement not covered" >    return dynamicMatch[1];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Try to match require statements
<span class="cstat-no" title="statement not covered" >  const requireMatch = importStatement.match(/require\(["']([^"']+)["']\)/);</span>
<span class="cstat-no" title="statement not covered" >  if (requireMatch) {</span>
<span class="cstat-no" title="statement not covered" >    return requireMatch[1];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Try to match regular imports
<span class="cstat-no" title="statement not covered" >  const importMatch = importStatement.match(/from\s+["']([^"']+)["']/);</span>
<span class="cstat-no" title="statement not covered" >  if (importMatch) {</span>
<span class="cstat-no" title="statement not covered" >    return importMatch[1];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Try to match direct imports (like import 'firebase/auth')
<span class="cstat-no" title="statement not covered" >  const directMatch = importStatement.match(/import\s+["']([^"']+)["']/);</span>
<span class="cstat-no" title="statement not covered" >  if (directMatch) {</span>
<span class="cstat-no" title="statement not covered" >    return directMatch[1];</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  return null;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper to extract package version from package.json if available
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function extractPackageVersion(packageName: string, baseDir: string): Promise&lt;string | undefined&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >  try {</span>
    // Handle scoped packages by getting the basic package name
<span class="cstat-no" title="statement not covered" >    let basicPackageName = packageName;</span>
<span class="cstat-no" title="statement not covered" >    if (packageName.startsWith('@')) {</span>
      // For scoped packages like @supabase/supabase-js, extract the scope part
<span class="cstat-no" title="statement not covered" >      const parts = packageName.split('/');</span>
<span class="cstat-no" title="statement not covered" >      if (parts.length &gt; 1) {</span>
        // Keep the scoped name as is
<span class="cstat-no" title="statement not covered" >        basicPackageName = packageName;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } else if (packageName.includes('/')) {</span>
      // For imports like 'firebase/auth', extract the base package
<span class="cstat-no" title="statement not covered" >      basicPackageName = packageName.split('/')[0];</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    const packageJsonPath = path.join(baseDir, 'package.json');</span>
<span class="cstat-no" title="statement not covered" >    const content = await fsPromises.readFile(packageJsonPath, 'utf-8');</span>
<span class="cstat-no" title="statement not covered" >    const packageData = JSON.parse(content);</span>
    
    // Check both dependencies and devDependencies
<span class="cstat-no" title="statement not covered" >    if (packageData.dependencies &amp;&amp; packageData.dependencies[basicPackageName]) {</span>
<span class="cstat-no" title="statement not covered" >      return packageData.dependencies[basicPackageName];</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    if (packageData.devDependencies &amp;&amp; packageData.devDependencies[basicPackageName]) {</span>
<span class="cstat-no" title="statement not covered" >      return packageData.devDependencies[basicPackageName];</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    return undefined;</span>
<span class="cstat-no" title="statement not covered" >  } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >    log(`Failed to extract package version for ${packageName}: ${error}`);</span>
<span class="cstat-no" title="statement not covered" >    return undefined;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper function to check if a path matches any exclude pattern
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function isExcluded(filePath: string, baseDir: string): boolean {</span></span>
  // Add a failsafe check specifically for .git directory
<span class="cstat-no" title="statement not covered" >  if (filePath.includes('.git') || path.basename(filePath) === '.git') {</span>
<span class="cstat-no" title="statement not covered" >    log(`🔴 SPECIAL CASE: .git directory/file detected: ${filePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return true;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Add a failsafe check for node_modules
<span class="cstat-no" title="statement not covered" >  if (filePath.includes('node_modules') || path.basename(filePath) === 'node_modules') {</span>
<span class="cstat-no" title="statement not covered" >    log(`🔴 SPECIAL CASE: node_modules directory/file detected: ${filePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return true;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Add a failsafe check for test_excluded files
<span class="cstat-no" title="statement not covered" >  if (filePath.includes('test_excluded') || path.basename(filePath).startsWith('test_excluded')) {</span>
<span class="cstat-no" title="statement not covered" >    log(`🔴 SPECIAL CASE: test_excluded file detected: ${filePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return true;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  log(`\n===== EXCLUDE CHECK for: ${filePath} =====`);</span>
  
<span class="cstat-no" title="statement not covered" >  const config = getConfig();</span>
<span class="cstat-no" title="statement not covered" >  if (!config) {</span>
<span class="cstat-no" title="statement not covered" >    log('❌ ERROR: Config is null! Global state not initialized properly.');</span>
<span class="cstat-no" title="statement not covered" >    return false;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  if (!config.excludePatterns || config.excludePatterns.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >    log('❌ WARNING: No exclude patterns found in config!');</span>
<span class="cstat-no" title="statement not covered" >    log(`Config object: ${JSON.stringify(config, null, 2)}`);</span>
<span class="cstat-no" title="statement not covered" >    return false;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Get relative path for matching, normalize to forward slashes for cross-platform consistency
<span class="cstat-no" title="statement not covered" >  const relativePath = path.relative(baseDir, filePath).replace(/\\/g, '/');</span>
<span class="cstat-no" title="statement not covered" >  const fileName = path.basename(filePath);</span>
  
<span class="cstat-no" title="statement not covered" >  log(`📂 Path details:`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Full path: ${filePath}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Base dir: ${baseDir}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Relative path: ${relativePath}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - File name: ${fileName}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Platform: ${process.platform}, path separator: ${path.sep}`);</span>
  
<span class="cstat-no" title="statement not covered" >  log(`\n🔍 Testing against ${config.excludePatterns.length} exclude patterns...`);</span>
  
  // Special case check for .git and node_modules
<span class="cstat-no" title="statement not covered" >  if (relativePath.includes('/.git/') || relativePath === '.git' || </span>
<span class="cstat-no" title="statement not covered" >      fileName === '.git' || relativePath.startsWith('.git/')) {</span>
<span class="cstat-no" title="statement not covered" >    log(`✅ MATCH! Special case for .git directory detected: ${relativePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return true;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  if (relativePath.includes('/node_modules/') || relativePath === 'node_modules' || </span>
<span class="cstat-no" title="statement not covered" >      fileName === 'node_modules' || relativePath.startsWith('node_modules/')) {</span>
<span class="cstat-no" title="statement not covered" >    log(`✅ MATCH! Special case for node_modules directory detected: ${relativePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return true;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Check each exclude pattern
<span class="cstat-no" title="statement not covered" >  for (let i = 0; i &lt; config.excludePatterns.length; i++) {</span>
<span class="cstat-no" title="statement not covered" >    const pattern = config.excludePatterns[i];</span>
<span class="cstat-no" title="statement not covered" >    log(`\n  [${i+1}/${config.excludePatterns.length}] Testing pattern: "${pattern}"`);</span>
    
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      const regex = globToRegExp(pattern);</span>
      //log(`  - Converted to regex: ${regex}`); // Uncomment for debugging
      
      // Test against full relative path
<span class="cstat-no" title="statement not covered" >      const fullPathMatch = regex.test(relativePath);</span>
      //log(`  - Match against relative path: ${fullPathMatch ? '✅ YES' : '❌ NO'}`); // Uncomment for debugging
      
<span class="cstat-no" title="statement not covered" >      if (fullPathMatch) {</span>
<span class="cstat-no" title="statement not covered" >        log(`✅ MATCH! Path ${relativePath} matches exclude pattern ${pattern}`);</span>
<span class="cstat-no" title="statement not covered" >        return true;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
      
      // Also test against just the filename for file extension patterns
<span class="cstat-no" title="statement not covered" >      if (pattern.startsWith('**/*.') || pattern.includes('/*.')) {</span>
<span class="cstat-no" title="statement not covered" >        const filenameMatch = regex.test(fileName);</span>
        //log(`  - Match against filename only: ${filenameMatch ? '✅ YES' : '❌ NO'}`); // Uncomment for debugging
        
<span class="cstat-no" title="statement not covered" >        if (filenameMatch) {</span>
<span class="cstat-no" title="statement not covered" >          log(`✅ MATCH! Filename ${fileName} matches exclude pattern ${pattern}`);</span>
<span class="cstat-no" title="statement not covered" >          return true;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >      log(`  - ❌ ERROR converting pattern to regex: ${error}`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  log(`❌ No pattern matches found for ${relativePath}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`===== END EXCLUDE CHECK =====\n`);</span>
<span class="cstat-no" title="statement not covered" >  return false;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Helper function to convert glob pattern to RegExp
export function globToRegExp(pattern: string): RegExp {
  //log(`  Converting glob pattern: ${pattern}`); // Uncomment for debugging
&nbsp;
  // Escape special regex characters except * and ?
  const escaped = pattern.replace(/[.+^${}()|[\]\\]/g, '\\$&amp;');
  //log(`  - After escaping special chars: ${escaped}`); // Uncomment for debugging
&nbsp;
  // Handle patterns starting with **/
  let prefix = '';
  if (escaped.startsWith('**/')) {
    // Make the initial part optional to match root level
    prefix = '(?:.*/)?';
    // Remove the leading **/ from the pattern being converted
    pattern = escaped.substring(3);
  } else {
    // Make the initial part optional for patterns not starting with **/
    prefix = '(?:.*/)?';
    pattern = escaped;
  }
&nbsp;
  // Convert glob patterns to regex patterns (applied to the potentially shortened pattern)
  const converted = pattern
    // Convert ** to special marker (use a different marker to avoid conflict)
    .replace(/\*\*/g, '__GLOBSTAR__')
    // Convert remaining * to [^/\\]*
    .replace(/\*/g, '[^/\\\\]*')
    // Convert ? to single character match
    .replace(/\?/g, '[^/\\\\]')
    // Convert globstar back to proper pattern
    .replace(/__GLOBSTAR__/g, '.*');
&nbsp;
  //log(`  - After pattern conversion: ${converted}`); // Uncomment for debugging
&nbsp;
  // Create regex that matches entire path, adding the optional prefix
  // Ensure the pattern is anchored correctly
  const finalPattern = `^${prefix}${converted}$`;
  const regex = new RegExp(finalPattern, 'i');
  //log(`  - Final regex: ${regex}`); // Uncomment for debugging
  return regex;
}
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >export async function scanDirectory(baseDir: string, currentDir: string = baseDir): Promise&lt;FileNode&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >  log(`\n📁 SCAN DIRECTORY: ${currentDir}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Base dir: ${baseDir}`);</span>
&nbsp;
  // Handle special case for current directory
<span class="cstat-no" title="statement not covered" >  const normalizedBaseDir = path.normalize(baseDir);</span>
<span class="cstat-no" title="statement not covered" >  const normalizedDirPath = path.normalize(currentDir);</span>
  
<span class="cstat-no" title="statement not covered" >  log(`  - Normalized base dir: ${normalizedBaseDir}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  - Normalized current dir: ${normalizedDirPath}`);</span>
&nbsp;
  // Create root node for this directory
<span class="cstat-no" title="statement not covered" >  const rootNode: FileNode = {</span>
<span class="cstat-no" title="statement not covered" >    path: normalizedDirPath,</span>
<span class="cstat-no" title="statement not covered" >    name: path.basename(normalizedDirPath),</span>
<span class="cstat-no" title="statement not covered" >    isDirectory: true,</span>
<span class="cstat-no" title="statement not covered" >    children: []</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
  // Read directory entries
<span class="cstat-no" title="statement not covered" >  let entries: fs.Dirent[];</span>
<span class="cstat-no" title="statement not covered" >  try {</span>
<span class="cstat-no" title="statement not covered" >    entries = await fsPromises.readdir(normalizedDirPath, { withFileTypes: true });</span>
<span class="cstat-no" title="statement not covered" >    log(`  - Read ${entries.length} entries in directory`);</span>
<span class="cstat-no" title="statement not covered" >  } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >    log(`  - ❌ Error reading directory ${normalizedDirPath}:`, error);</span>
<span class="cstat-no" title="statement not covered" >    return rootNode;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Process each entry
<span class="cstat-no" title="statement not covered" >  let excluded = 0;</span>
<span class="cstat-no" title="statement not covered" >  let included = 0;</span>
<span class="cstat-no" title="statement not covered" >  let dirProcessed = 0;</span>
<span class="cstat-no" title="statement not covered" >  let fileProcessed = 0;</span>
  
<span class="cstat-no" title="statement not covered" >  log(`\n  Processing ${entries.length} entries in ${normalizedDirPath}...`);</span>
  
  // ==================== CRITICAL CODE ====================
  // Log the global config status before processing entries
<span class="cstat-no" title="statement not covered" >  log(`\n🔍 BEFORE PROCESSING: Is config loaded? ${getConfig() !== null ? 'YES ✅' : 'NO ❌'}`);</span>
<span class="cstat-no" title="statement not covered" >  if (getConfig()) {</span>
<span class="cstat-no" title="statement not covered" >    const excludePatternsLength = getConfig()?.excludePatterns?.length || 0;</span>
<span class="cstat-no" title="statement not covered" >    log(`  - Exclude patterns count: ${excludePatternsLength}`);</span>
<span class="cstat-no" title="statement not covered" >    if (excludePatternsLength &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      log(`  - First few patterns: ${getConfig()?.excludePatterns?.slice(0, 3).join(', ')}`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  // ======================================================
  
<span class="cstat-no" title="statement not covered" >  for (const entry of entries) {</span>
<span class="cstat-no" title="statement not covered" >    const fullPath = path.join(normalizedDirPath, entry.name);</span>
<span class="cstat-no" title="statement not covered" >    const normalizedFullPath = path.normalize(fullPath);</span>
    
<span class="cstat-no" title="statement not covered" >    log(`\n  Entry: ${entry.name} (${entry.isDirectory() ? 'directory' : 'file'})`);</span>
<span class="cstat-no" title="statement not covered" >    log(`  - Full path: ${normalizedFullPath}`);</span>
&nbsp;
    // Here's the critical exclusion check
<span class="cstat-no" title="statement not covered" >    log(`  🔍 Checking if path should be excluded: ${normalizedFullPath}`);</span>
<span class="cstat-no" title="statement not covered" >    const shouldExclude = isExcluded(normalizedFullPath, normalizedBaseDir);</span>
<span class="cstat-no" title="statement not covered" >    log(`  🔍 Exclusion check result: ${shouldExclude ? 'EXCLUDE ✅' : 'INCLUDE ❌'}`);</span>
    
<span class="cstat-no" title="statement not covered" >    if (shouldExclude) {</span>
<span class="cstat-no" title="statement not covered" >      log(`  - ✅ Skipping excluded path: ${normalizedFullPath}`);</span>
<span class="cstat-no" title="statement not covered" >      excluded++;</span>
<span class="cstat-no" title="statement not covered" >      continue;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    log(`  - ✅ Including path: ${normalizedFullPath}`);</span>
<span class="cstat-no" title="statement not covered" >    included++;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (entry.isDirectory()) {</span>
<span class="cstat-no" title="statement not covered" >      log(`  - Processing directory: ${normalizedFullPath}`);</span>
<span class="cstat-no" title="statement not covered" >      const childNode = await scanDirectory(normalizedBaseDir, fullPath);</span>
<span class="cstat-no" title="statement not covered" >      rootNode.children?.push(childNode);</span>
<span class="cstat-no" title="statement not covered" >      dirProcessed++;</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      log(`  - Processing file: ${normalizedFullPath}`);</span>
<span class="cstat-no" title="statement not covered" >      fileProcessed++;</span>
<span class="cstat-no" title="statement not covered" >      const ext = path.extname(entry.name);</span>
<span class="cstat-no" title="statement not covered" >      const importPattern = IMPORT_PATTERNS[ext];</span>
<span class="cstat-no" title="statement not covered" >      const dependencies: string[] = [];</span>
<span class="cstat-no" title="statement not covered" >      const packageDependencies: PackageDependency[] = [];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (importPattern) {</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >          const content = await fsPromises.readFile(fullPath, 'utf-8');</span>
<span class="cstat-no" title="statement not covered" >          const matches = content.match(importPattern);</span>
<span class="cstat-no" title="statement not covered" >          log(`Found ${matches?.length || 0} potential imports in ${normalizedFullPath}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (matches) {</span>
<span class="cstat-no" title="statement not covered" >            for (const match of matches) {</span>
<span class="cstat-no" title="statement not covered" >              const importPath = extractImportPath(match);</span>
<span class="cstat-no" title="statement not covered" >              if (importPath) {</span>
                // Skip if the importPath looks like an unresolved template literal
<span class="cstat-no" title="statement not covered" >                if (isUnresolvedTemplateLiteral(importPath)) {</span>
<span class="cstat-no" title="statement not covered" >                  log(`Skipping unresolved template literal: ${importPath}`);</span>
<span class="cstat-no" title="statement not covered" >                  continue;</span>
<span class="cstat-no" title="statement not covered" >                }</span>
                
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                  let resolvedPath;</span>
<span class="cstat-no" title="statement not covered" >                  if (['.js', '.jsx', '.ts', '.tsx'].includes(ext)) {</span>
<span class="cstat-no" title="statement not covered" >                    resolvedPath = resolveImportPath(importPath, normalizedFullPath, normalizedBaseDir);</span>
<span class="cstat-no" title="statement not covered" >                  } else {</span>
<span class="cstat-no" title="statement not covered" >                    resolvedPath = path.resolve(path.dirname(fullPath), importPath);</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                  log(`Resolved path: ${resolvedPath}`);</span>
                  
                  // Handle package imports
<span class="cstat-no" title="statement not covered" >                  if (resolvedPath.includes('node_modules') || importPath.startsWith('@') || (!importPath.startsWith('.') &amp;&amp; !importPath.startsWith('/'))) {</span>
                    // Create a package dependency object with more information
<span class="cstat-no" title="statement not covered" >                    const pkgDep = PackageDependency.fromPath(resolvedPath);</span>
                    
                    // Set the package name directly from the import path if it's empty
<span class="cstat-no" title="statement not covered" >                    if (!pkgDep.name) {</span>
                      // Skip if the importPath looks like an unresolved template literal
<span class="cstat-no" title="statement not covered" >                      if (isUnresolvedTemplateLiteral(importPath)) {</span>
<span class="cstat-no" title="statement not covered" >                        log(`Skipping package dependency with template literal name: ${importPath}`);</span>
<span class="cstat-no" title="statement not covered" >                        continue;</span>
<span class="cstat-no" title="statement not covered" >                      }</span>
                      
                      // For imports like '@scope/package'
<span class="cstat-no" title="statement not covered" >                      if (importPath.startsWith('@')) {</span>
<span class="cstat-no" title="statement not covered" >                        const parts = importPath.split('/');</span>
<span class="cstat-no" title="statement not covered" >                        if (parts.length &gt;= 2) {</span>
<span class="cstat-no" title="statement not covered" >                          pkgDep.scope = parts[0];</span>
<span class="cstat-no" title="statement not covered" >                          pkgDep.name = `${parts[0]}/${parts[1]}`;</span>
<span class="cstat-no" title="statement not covered" >                        }</span>
<span class="cstat-no" title="statement not covered" >                      } </span>
                      // For imports like 'package'
<span class="cstat-no" title="statement not covered" >                      else if (importPath.includes('/')) {</span>
<span class="cstat-no" title="statement not covered" >                        pkgDep.name = importPath.split('/')[0];</span>
<span class="cstat-no" title="statement not covered" >                      } else {</span>
<span class="cstat-no" title="statement not covered" >                        pkgDep.name = importPath;</span>
<span class="cstat-no" title="statement not covered" >                      }</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
                    
                    // Skip if the resolved package name is a template literal
<span class="cstat-no" title="statement not covered" >                    if (isUnresolvedTemplateLiteral(pkgDep.name)) {</span>
<span class="cstat-no" title="statement not covered" >                      log(`Skipping package with template literal name: ${pkgDep.name}`);</span>
<span class="cstat-no" title="statement not covered" >                      continue;</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
                    
                    // Try to extract version information
<span class="cstat-no" title="statement not covered" >                    if (pkgDep.name) {</span>
<span class="cstat-no" title="statement not covered" >                      const version = await extractPackageVersion(pkgDep.name, normalizedBaseDir);</span>
<span class="cstat-no" title="statement not covered" >                      if (version) {</span>
<span class="cstat-no" title="statement not covered" >                        pkgDep.version = version;</span>
<span class="cstat-no" title="statement not covered" >                      }</span>
                      
                      // Check if it's a dev dependency
<span class="cstat-no" title="statement not covered" >                      try {</span>
<span class="cstat-no" title="statement not covered" >                        const packageJsonPath = path.join(normalizedBaseDir, 'package.json');</span>
<span class="cstat-no" title="statement not covered" >                        const content = await fsPromises.readFile(packageJsonPath, 'utf-8');</span>
<span class="cstat-no" title="statement not covered" >                        const packageData = JSON.parse(content);</span>
                        
<span class="cstat-no" title="statement not covered" >                        if (packageData.devDependencies &amp;&amp; packageData.devDependencies[pkgDep.name]) {</span>
<span class="cstat-no" title="statement not covered" >                          pkgDep.isDevDependency = true;</span>
<span class="cstat-no" title="statement not covered" >                        }</span>
<span class="cstat-no" title="statement not covered" >                      } catch (error) {</span>
                        // Ignore package.json errors
<span class="cstat-no" title="statement not covered" >                      }</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
                    
<span class="cstat-no" title="statement not covered" >                    packageDependencies.push(pkgDep);</span>
<span class="cstat-no" title="statement not covered" >                    continue;</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
                  
                  // Try with different extensions for TypeScript/JavaScript files
<span class="cstat-no" title="statement not covered" >                  const possibleExtensions = ['.ts', '.tsx', '.js', '.jsx', ''];</span>
<span class="cstat-no" title="statement not covered" >                  for (const extension of possibleExtensions) {</span>
<span class="cstat-no" title="statement not covered" >                    const pathToCheck = resolvedPath + extension;</span>
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      await fsPromises.access(pathToCheck);</span>
<span class="cstat-no" title="statement not covered" >                      log(`Found existing path: ${pathToCheck}`);</span>
<span class="cstat-no" title="statement not covered" >                      dependencies.push(pathToCheck);</span>
<span class="cstat-no" title="statement not covered" >                      break;</span>
<span class="cstat-no" title="statement not covered" >                    } catch {</span>
                      // File doesn't exist with this extension, try next one
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >                } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >                  log(`Failed to resolve path for ${importPath}:`, error);</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        } catch (error) {</span>
<span class="cstat-no" title="statement not covered" >          log(`Failed to read or process file ${fullPath}:`, error);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      const fileNode: FileNode = {</span>
<span class="cstat-no" title="statement not covered" >        path: normalizedFullPath,</span>
<span class="cstat-no" title="statement not covered" >        name: entry.name,</span>
<span class="cstat-no" title="statement not covered" >        isDirectory: false,</span>
<span class="cstat-no" title="statement not covered" >        importance: calculateInitialImportance(normalizedFullPath, normalizedBaseDir),</span>
<span class="cstat-no" title="statement not covered" >        dependencies: dependencies,</span>
<span class="cstat-no" title="statement not covered" >        packageDependencies: packageDependencies,</span>
<span class="cstat-no" title="statement not covered" >        dependents: [],</span>
<span class="cstat-no" title="statement not covered" >        summary: undefined</span>
<span class="cstat-no" title="statement not covered" >      };</span>
<span class="cstat-no" title="statement not covered" >      rootNode.children?.push(fileNode);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Log summary for this directory
<span class="cstat-no" title="statement not covered" >  log(`\n  📊 DIRECTORY SCAN SUMMARY for ${normalizedDirPath}:`);</span>
<span class="cstat-no" title="statement not covered" >  log(`    - Total entries: ${entries.length}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`    - Excluded: ${excluded}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`    - Included: ${included}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`    - Directories processed: ${dirProcessed}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`    - Files processed: ${fileProcessed}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`  📁 END SCAN DIRECTORY: ${currentDir}\n`);</span>
  
<span class="cstat-no" title="statement not covered" >  return rootNode;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Find all file nodes in the tree
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function getAllFileNodes(root: FileNode): FileNode[] {</span></span>
<span class="cstat-no" title="statement not covered" >  const results: FileNode[] = [];</span>
  
<span class="cstat-no" title="statement not covered" >  function traverse(node: FileNode) {</span>
<span class="cstat-no" title="statement not covered" >    if (!node.isDirectory) {</span>
<span class="cstat-no" title="statement not covered" >      results.push(node);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (node.children) {</span>
<span class="cstat-no" title="statement not covered" >      node.children.forEach(traverse);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  traverse(root);</span>
<span class="cstat-no" title="statement not covered" >  return results;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Build the reverse dependency map (dependents)
export <span class="fstat-no" title="function not covered" >function buildDependentMap(root: FileNode) {</span>
<span class="cstat-no" title="statement not covered" >  const allFiles = getAllFileNodes(root);</span>
<span class="cstat-no" title="statement not covered" >  const pathToNodeMap = new Map&lt;string, FileNode&gt;();</span>
  
  // First, create a map of all file paths to their nodes
<span class="cstat-no" title="statement not covered" >  allFiles.forEach(file =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    pathToNodeMap.set(file.path, file);</span>
<span class="cstat-no" title="statement not covered" >  });</span>
  
  // Then, process dependencies to create the reverse mapping
<span class="cstat-no" title="statement not covered" >  allFiles.forEach(file =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (file.dependencies &amp;&amp; file.dependencies.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      file.dependencies.forEach(depPath =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const depNode = pathToNodeMap.get(depPath);</span>
<span class="cstat-no" title="statement not covered" >        if (depNode) {</span>
<span class="cstat-no" title="statement not covered" >          if (!depNode.dependents) {</span>
<span class="cstat-no" title="statement not covered" >            depNode.dependents = [];</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >          if (!depNode.dependents.includes(file.path)) {</span>
<span class="cstat-no" title="statement not covered" >            depNode.dependents.push(file.path);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      });</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  });</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
export <span class="fstat-no" title="function not covered" >function calculateImportance(node: FileNode): void {</span>
<span class="cstat-no" title="statement not covered" >  if (!node.isDirectory) {</span>
    // Start with initial importance
<span class="cstat-no" title="statement not covered" >    let importance = node.importance || calculateInitialImportance(node.path, process.cwd());</span>
    
    // Add importance based on number of dependents (files that import this file)
<span class="cstat-no" title="statement not covered" >    if (node.dependents &amp;&amp; node.dependents.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      importance += Math.min(node.dependents.length, 3);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Add importance based on number of local dependencies (files this file imports)
<span class="cstat-no" title="statement not covered" >    if (node.dependencies &amp;&amp; node.dependencies.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >      importance += Math.min(node.dependencies.length, 2);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Add importance based on number of package dependencies
<span class="cstat-no" title="statement not covered" >    if (node.packageDependencies &amp;&amp; node.packageDependencies.length &gt; 0) {</span>
      // Add more importance for SDK dependencies
<span class="cstat-no" title="statement not covered" >      const sdkDeps = node.packageDependencies.filter(dep =&gt; dep.name &amp;&amp; dep.name.includes('@modelcontextprotocol/sdk'));</span>
<span class="cstat-no" title="statement not covered" >      const otherDeps = node.packageDependencies.filter(dep =&gt; dep.name &amp;&amp; !dep.name.includes('@modelcontextprotocol/sdk'));</span>
      
<span class="cstat-no" title="statement not covered" >      importance += Math.min(sdkDeps.length, 2); // SDK dependencies are more important</span>
<span class="cstat-no" title="statement not covered" >      importance += Math.min(otherDeps.length, 1); // Other package dependencies</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Cap importance at 10
<span class="cstat-no" title="statement not covered" >    node.importance = Math.min(importance, 10);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // Recursively calculate importance for children
<span class="cstat-no" title="statement not covered" >  if (node.children) {</span>
<span class="cstat-no" title="statement not covered" >    for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >      calculateImportance(child);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// Add a function to manually set importance
export <span class="fstat-no" title="function not covered" >function setFileImportance(fileTree: FileNode, filePath: string, importance: number): boolean {</span>
<span class="cstat-no" title="statement not covered" >  const normalizedInputPath = normalizePath(filePath);</span>
<span class="cstat-no" title="statement not covered" >  log(`Setting importance for file: ${normalizedInputPath}`);</span>
<span class="cstat-no" title="statement not covered" >  log(`Current tree root: ${fileTree.path}`);</span>
  
<span class="cstat-no" title="statement not covered" >  function findAndSetImportance(node: FileNode): boolean {</span>
<span class="cstat-no" title="statement not covered" >    const normalizedNodePath = normalizePath(node.path);</span>
<span class="cstat-no" title="statement not covered" >    log(`Checking node: ${normalizedNodePath}`);</span>
    
    // Try exact match
<span class="cstat-no" title="statement not covered" >    if (normalizedNodePath === normalizedInputPath) {</span>
<span class="cstat-no" title="statement not covered" >      log(`Found exact match for: ${normalizedInputPath}`);</span>
<span class="cstat-no" title="statement not covered" >      node.importance = Math.min(10, Math.max(0, importance));</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Try case-insensitive match for Windows compatibility
<span class="cstat-no" title="statement not covered" >    if (normalizedNodePath.toLowerCase() === normalizedInputPath.toLowerCase()) {</span>
<span class="cstat-no" title="statement not covered" >      log(`Found case-insensitive match for: ${normalizedInputPath}`);</span>
<span class="cstat-no" title="statement not covered" >      node.importance = Math.min(10, Math.max(0, importance));</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Check if the path ends with our target (to handle relative vs absolute paths)
<span class="cstat-no" title="statement not covered" >    if (normalizedInputPath.endsWith(normalizedNodePath) || normalizedNodePath.endsWith(normalizedInputPath)) {</span>
<span class="cstat-no" title="statement not covered" >      log(`Found path suffix match for: ${normalizedInputPath}`);</span>
<span class="cstat-no" title="statement not covered" >      node.importance = Math.min(10, Math.max(0, importance));</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Try with basename
<span class="cstat-no" title="statement not covered" >    const inputBasename = normalizedInputPath.split('/').pop() || '';</span>
<span class="cstat-no" title="statement not covered" >    const nodeBasename = normalizedNodePath.split('/').pop() || '';</span>
<span class="cstat-no" title="statement not covered" >    if (nodeBasename === inputBasename &amp;&amp; nodeBasename !== '') {</span>
<span class="cstat-no" title="statement not covered" >      log(`Found basename match for: ${inputBasename}`);</span>
<span class="cstat-no" title="statement not covered" >      node.importance = Math.min(10, Math.max(0, importance));</span>
<span class="cstat-no" title="statement not covered" >      return true;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    if (node.isDirectory &amp;&amp; node.children) {</span>
<span class="cstat-no" title="statement not covered" >      for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >        if (findAndSetImportance(child)) {</span>
<span class="cstat-no" title="statement not covered" >          return true;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    return false;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  return findAndSetImportance(fileTree);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >export async function createFileTree(baseDir: string): Promise&lt;FileNode&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >  const normalizedBaseDir = path.normalize(baseDir);</span>
<span class="cstat-no" title="statement not covered" >  const nodes = await scanDirectory(normalizedBaseDir);</span>
  
  // The first node should be the root directory
<span class="cstat-no" title="statement not covered" >  if (nodes.isDirectory &amp;&amp; nodes.path === normalizedBaseDir) {</span>
<span class="cstat-no" title="statement not covered" >    return nodes;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
  // If for some reason we didn't get a root node, create one
<span class="cstat-no" title="statement not covered" >  const rootNode: FileNode = {</span>
<span class="cstat-no" title="statement not covered" >    path: normalizedBaseDir,</span>
<span class="cstat-no" title="statement not covered" >    name: path.basename(normalizedBaseDir),</span>
<span class="cstat-no" title="statement not covered" >    isDirectory: true,</span>
<span class="cstat-no" title="statement not covered" >    children: []</span>
<span class="cstat-no" title="statement not covered" >  };</span>
  
  // Add all nodes that don't have a parent
<span class="cstat-no" title="statement not covered" >  for (const node of nodes.children || []) {</span>
<span class="cstat-no" title="statement not covered" >    if (path.dirname(node.path) === normalizedBaseDir) {</span>
<span class="cstat-no" title="statement not covered" >      rootNode.children?.push(node);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  return rootNode;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
export <span class="fstat-no" title="function not covered" >function getFileImportance(fileTree: FileNode, targetPath: string): FileNode | null {</span>
<span class="cstat-no" title="statement not covered" >  const normalizedInputPath = normalizePath(targetPath);</span>
<span class="cstat-no" title="statement not covered" >  log(`Looking for file: ${normalizedInputPath}`);</span>
  
<span class="cstat-no" title="statement not covered" >  function findNode(node: FileNode, targetPath: string): FileNode | null {</span>
    // Normalize paths to handle both forward and backward slashes
<span class="cstat-no" title="statement not covered" >    const normalizedTargetPath = path.normalize(targetPath).toLowerCase();</span>
<span class="cstat-no" title="statement not covered" >    const normalizedNodePath = path.normalize(node.path).toLowerCase();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (normalizedNodePath === normalizedTargetPath) {</span>
<span class="cstat-no" title="statement not covered" >      return node;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (node.children) {</span>
<span class="cstat-no" title="statement not covered" >      for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >        const found = findNode(child, targetPath);</span>
<span class="cstat-no" title="statement not covered" >        if (found) return found;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return null;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  return findNode(fileTree, normalizedInputPath);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Finds a node in the file tree by its absolute path.
 * @param tree The file tree node to search within.
 * @param targetPath The absolute path of the node to find.
 * @returns The found FileNode or null if not found.
 */
export <span class="fstat-no" title="function not covered" >function findNodeByPath(tree: FileNode | null, targetPath: string): FileNode | null {</span>
<span class="cstat-no" title="statement not covered" >  if (!tree) return null;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const normalizedTargetPath = normalizePath(targetPath);</span>
<span class="cstat-no" title="statement not covered" >  const normalizedNodePath = normalizePath(tree.path);</span>
&nbsp;
  // Check the current node
<span class="cstat-no" title="statement not covered" >  if (normalizedNodePath === normalizedTargetPath) {</span>
<span class="cstat-no" title="statement not covered" >    return tree;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // If it's a directory, search its children
<span class="cstat-no" title="statement not covered" >  if (tree.isDirectory &amp;&amp; tree.children) {</span>
<span class="cstat-no" title="statement not covered" >    for (const child of tree.children) {</span>
<span class="cstat-no" title="statement not covered" >      const found = findNodeByPath(child, targetPath);</span>
<span class="cstat-no" title="statement not covered" >      if (found) {</span>
<span class="cstat-no" title="statement not covered" >        return found;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Node not found in this subtree
<span class="cstat-no" title="statement not covered" >  return null;</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// --- New Functions for Incremental Updates ---
&nbsp;
// Placeholder for dependency analysis of a single new file
// This needs to replicate the relevant logic from scanDirectory
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function analyzeNewFile(filePath: string, projectRoot: string): Promise&lt;{ dependencies: string[]; packageDependencies: PackageDependency[] }&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >  log(`[analyzeNewFile] Analyzing ${filePath}`);</span>
<span class="cstat-no" title="statement not covered" >  const dependencies: string[] = [];</span>
<span class="cstat-no" title="statement not covered" >  const packageDependencies: PackageDependency[] = [];</span>
<span class="cstat-no" title="statement not covered" >  const ext = path.extname(filePath);</span>
<span class="cstat-no" title="statement not covered" >  const pattern = IMPORT_PATTERNS[ext];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (pattern) {</span>
<span class="cstat-no" title="statement not covered" >     try {</span>
<span class="cstat-no" title="statement not covered" >       const content = await fsPromises.readFile(filePath, 'utf-8');</span>
<span class="cstat-no" title="statement not covered" >       let match;</span>
<span class="cstat-no" title="statement not covered" >       while ((match = pattern.exec(content)) !== null) {</span>
<span class="cstat-no" title="statement not covered" >         const importPath = match[1] || match[2] || match[3]; // Adjust indices based on specific regex</span>
<span class="cstat-no" title="statement not covered" >         if (importPath) {</span>
            // Skip if the importPath looks like an unresolved template literal
<span class="cstat-no" title="statement not covered" >            if (isUnresolvedTemplateLiteral(importPath)) {</span>
<span class="cstat-no" title="statement not covered" >              log(`[analyzeNewFile] Skipping unresolved template literal: ${importPath}`);</span>
<span class="cstat-no" title="statement not covered" >              continue;</span>
<span class="cstat-no" title="statement not covered" >            }</span>
            
<span class="cstat-no" title="statement not covered" >            try {</span>
<span class="cstat-no" title="statement not covered" >                const resolvedPath = resolveImportPath(importPath, filePath, projectRoot);</span>
<span class="cstat-no" title="statement not covered" >                const normalizedResolvedPath = normalizePath(resolvedPath);</span>
&nbsp;
                // Check if it's a package dependency (heuristic: includes node_modules or doesn't start with . or /)
<span class="cstat-no" title="statement not covered" >                if (normalizedResolvedPath.includes('node_modules') || (!importPath.startsWith('.') &amp;&amp; !importPath.startsWith('/'))) {</span>
<span class="cstat-no" title="statement not covered" >                    const pkgDep = PackageDependency.fromPath(normalizedResolvedPath);</span>
                    
                    // Skip if the package name is a template literal
<span class="cstat-no" title="statement not covered" >                    if (isUnresolvedTemplateLiteral(pkgDep.name)) {</span>
<span class="cstat-no" title="statement not covered" >                      log(`[analyzeNewFile] Skipping package with template literal name: ${pkgDep.name}`);</span>
<span class="cstat-no" title="statement not covered" >                      continue;</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
                    
<span class="cstat-no" title="statement not covered" >                    const version = await extractPackageVersion(pkgDep.name, projectRoot);</span>
<span class="cstat-no" title="statement not covered" >                    if (version) {</span>
<span class="cstat-no" title="statement not covered" >                      pkgDep.version = version;</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                    packageDependencies.push(pkgDep);</span>
<span class="cstat-no" title="statement not covered" >                } else {</span>
                    // Attempt to confirm local file exists (you might need more robust checking like in scanDirectory)
<span class="cstat-no" title="statement not covered" >                    try {</span>
<span class="cstat-no" title="statement not covered" >                      await fsPromises.access(normalizedResolvedPath);</span>
<span class="cstat-no" title="statement not covered" >                      dependencies.push(normalizedResolvedPath);</span>
<span class="cstat-no" title="statement not covered" >                    } catch {</span>
                      //console.warn(`[analyzeNewFile] Referenced local file not found: ${normalizedResolvedPath}`);
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            } catch (resolveError) {</span>
<span class="cstat-no" title="statement not covered" >                 log(`[analyzeNewFile] Error resolving import '${importPath}' in ${filePath}: ${resolveError}`);</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >         }</span>
<span class="cstat-no" title="statement not covered" >       }</span>
<span class="cstat-no" title="statement not covered" >     } catch (readError) {</span>
<span class="cstat-no" title="statement not covered" >       log(`[analyzeNewFile] Error reading file ${filePath}: ${readError}`);</span>
<span class="cstat-no" title="statement not covered" >     }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  log(`[analyzeNewFile] Found deps for ${filePath}: ${JSON.stringify({ dependencies, packageDependencies })}`);</span>
<span class="cstat-no" title="statement not covered" >  return { dependencies, packageDependencies };</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
&nbsp;
/**
 * Incrementally adds a new file node to the global file tree.
 * Analyzes the new file, calculates its importance, and updates relevant dependents.
 * Must be called with the currently active file tree and its config.
 * @param filePath The absolute path of the file to add.
 * @param activeFileTree The currently active FileNode tree.
 * @param activeProjectRoot The project root directory.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >export async function addFileNode(</span></span>
<span class="cstat-no" title="statement not covered" >    filePath: string,</span>
<span class="cstat-no" title="statement not covered" >    activeFileTree: FileNode,</span>
<span class="cstat-no" title="statement not covered" >    activeProjectRoot: string</span>
<span class="cstat-no" title="statement not covered" >): Promise&lt;void&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const normalizedFilePath = normalizePath(filePath);</span>
  // Removed reliance on getConfig() here
&nbsp;
<span class="cstat-no" title="statement not covered" >  log(`[addFileNode] Attempting to add file: ${normalizedFilePath} to tree rooted at ${activeFileTree.path}`);</span>
&nbsp;
  // 1. Find the parent directory node within the provided active tree
<span class="cstat-no" title="statement not covered" >  const parentDir = path.dirname(normalizedFilePath);</span>
<span class="cstat-no" title="statement not covered" >  const parentNode = findNodeByPath(activeFileTree, parentDir);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (!parentNode || !parentNode.isDirectory) {</span>
<span class="cstat-no" title="statement not covered" >    log(`[addFileNode] Could not find parent directory node for: ${normalizedFilePath}`);</span>
    // Optionally: Handle cases where intermediate directories might also need creation
<span class="cstat-no" title="statement not covered" >    return;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 2. Check if node already exists (should not happen if watcher is correct, but good practice)
<span class="cstat-no" title="statement not covered" >  if (parentNode.children?.some(child =&gt; normalizePath(child.path) === normalizedFilePath)) {</span>
<span class="cstat-no" title="statement not covered" >    log(`[addFileNode] Node already exists: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  try {</span>
    // 3. Create the new FileNode (Removed size, createdAt, modifiedAt)
<span class="cstat-no" title="statement not covered" >    const newNode = new FileNode(); // Use class constructor</span>
<span class="cstat-no" title="statement not covered" >    newNode.path = normalizedFilePath;</span>
<span class="cstat-no" title="statement not covered" >    newNode.name = path.basename(normalizedFilePath);</span>
<span class="cstat-no" title="statement not covered" >    newNode.isDirectory = false;</span>
<span class="cstat-no" title="statement not covered" >    newNode.dependencies = []; // Initialize as empty arrays</span>
<span class="cstat-no" title="statement not covered" >    newNode.packageDependencies = [];</span>
<span class="cstat-no" title="statement not covered" >    newNode.dependents = [];</span>
<span class="cstat-no" title="statement not covered" >    newNode.summary = '';</span>
&nbsp;
    // 4. Analyze the new file's content for dependencies
    // Use the placeholder analysis function
<span class="cstat-no" title="statement not covered" >    const { dependencies, packageDependencies } = await analyzeNewFile(normalizedFilePath, activeProjectRoot);</span>
<span class="cstat-no" title="statement not covered" >    newNode.dependencies = dependencies;</span>
<span class="cstat-no" title="statement not covered" >    newNode.packageDependencies = packageDependencies;</span>
&nbsp;
&nbsp;
    // 5. Calculate initial importance for the new node
    // Use the existing calculateInitialImportance function
<span class="cstat-no" title="statement not covered" >    newNode.importance = calculateInitialImportance(newNode.path, activeProjectRoot);</span>
&nbsp;
    // 6. Add the new node to the parent's children
<span class="cstat-no" title="statement not covered" >    if (!parentNode.children) {</span>
<span class="cstat-no" title="statement not covered" >      parentNode.children = [];</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    parentNode.children.push(newNode);</span>
<span class="cstat-no" title="statement not covered" >    parentNode.children.sort((a, b) =&gt; a.name.localeCompare(b.name)); // Keep sorted</span>
&nbsp;
    // 7. Update dependents lists of the files imported by the new node
<span class="cstat-no" title="statement not covered" >    await updateDependentsForNewNode(newNode, activeFileTree); // Pass active tree</span>
&nbsp;
&nbsp;
    // 8. Recalculate importance for affected nodes (new node and its dependencies)
    // Ensure dependencies is an array before mapping
<span class="cstat-no" title="statement not covered" >    const depPaths = (newNode.dependencies ?? []).map(d =&gt; normalizePath(d));</span>
<span class="cstat-no" title="statement not covered" >    await recalculateImportanceForAffected([newNode.path, ...depPaths], activeFileTree, activeProjectRoot); // Pass active tree &amp; root</span>
&nbsp;
    // 9. Global state update is handled by the caller (mcp-server) after saving
&nbsp;
<span class="cstat-no" title="statement not covered" >    log(`[addFileNode] Successfully added node: ${normalizedFilePath}`);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  } catch (error: any) {</span>
<span class="cstat-no" title="statement not covered" >    if (error.code === 'ENOENT') {</span>
<span class="cstat-no" title="statement not covered" >       log(`[addFileNode] File not found during add operation (might have been deleted quickly): ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >       log(`[addFileNode] Error adding file node ${normalizedFilePath}:`, error);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Incrementally removes a file node from the global file tree.
 * Updates dependents of the removed file and the files it depended on.
 * Must be called with the currently active file tree.
 * @param filePath The absolute path of the file to remove.
 * @param activeFileTree The currently active FileNode tree.
 * @param activeProjectRoot The project root directory.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >export async function removeFileNode(</span></span>
<span class="cstat-no" title="statement not covered" >    filePath: string,</span>
<span class="cstat-no" title="statement not covered" >    activeFileTree: FileNode,</span>
<span class="cstat-no" title="statement not covered" >    activeProjectRoot: string</span>
<span class="cstat-no" title="statement not covered" >): Promise&lt;void&gt; {</span>
  // Check if filePath is a relative path, and if so, resolve it to an absolute path
<span class="cstat-no" title="statement not covered" >  let absoluteFilePath = filePath;</span>
<span class="cstat-no" title="statement not covered" >  if (!path.isAbsolute(filePath)) {</span>
<span class="cstat-no" title="statement not covered" >    absoluteFilePath = path.join(activeProjectRoot, filePath);</span>
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] Converted relative path "${filePath}" to absolute path "${absoluteFilePath}"`);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  const normalizedFilePath = normalizePath(absoluteFilePath);</span>
<span class="cstat-no" title="statement not covered" >  log(`[removeFileNode] Attempting to remove file: ${normalizedFilePath} from tree rooted at ${activeFileTree.path}`);</span>
&nbsp;
  // Log the current state of the file tree - fix this by converting to string
  // log(`Current file tree state before removal: ${JSON.stringify(activeFileTree, null, 2)}`);
&nbsp;
  // 1. Find the node to remove within the provided active tree
<span class="cstat-no" title="statement not covered" >  const nodeToRemove = findNodeByPath(activeFileTree, normalizedFilePath);</span>
  
  // If node not found, try looking it up by basename as a fallback
<span class="cstat-no" title="statement not covered" >  if (!nodeToRemove || nodeToRemove.isDirectory) {</span>
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] Initial search failed for: ${normalizedFilePath}`);</span>
    
    // Fallback: Find by basename in case of relative path issues
<span class="cstat-no" title="statement not covered" >    const basename = path.basename(normalizedFilePath);</span>
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] Trying fallback search by basename: ${basename}`);</span>
    
    // Get all file nodes and search by basename
<span class="cstat-no" title="statement not covered" >    const allFileNodes = getAllFileNodes(activeFileTree);</span>
<span class="cstat-no" title="statement not covered" >    const nodeByName = allFileNodes.find(node =&gt; </span>
<span class="cstat-no" title="statement not covered" >      !node.isDirectory &amp;&amp; path.basename(node.path) === basename</span>
<span class="cstat-no" title="statement not covered" >    );</span>
    
<span class="cstat-no" title="statement not covered" >    if (nodeByName) {</span>
<span class="cstat-no" title="statement not covered" >      log(`[removeFileNode] Found node by basename: ${nodeByName.path}`);</span>
      // Call removeFileNode recursively with the found absolute path
<span class="cstat-no" title="statement not covered" >      return removeFileNode(nodeByName.path, activeFileTree, activeProjectRoot);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // If still not found, report an error
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] File node not found or is a directory: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  log(`[removeFileNode] Found node to remove: ${nodeToRemove.path}`);</span>
&nbsp;
  // 2. Find the parent directory node within the provided active tree
<span class="cstat-no" title="statement not covered" >  const parentDir = path.dirname(normalizedFilePath);</span>
<span class="cstat-no" title="statement not covered" >  const parentNode = findNodeByPath(activeFileTree, parentDir);</span>
<span class="cstat-no" title="statement not covered" >  if (!parentNode || !parentNode.isDirectory || !parentNode.children) {</span>
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] Could not find parent directory node for: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >    return;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  log(`[removeFileNode] Found parent node: ${parentNode.path}`);</span>
&nbsp;
  // 3. Store necessary info before removal (Ensure arrays exist)
<span class="cstat-no" title="statement not covered" >  const dependenciesToRemoveFrom = [...(nodeToRemove.dependencies ?? [])];</span>
<span class="cstat-no" title="statement not covered" >  const dependentsToUpdate = [...(nodeToRemove.dependents ?? [])]; // Files that depended on this node</span>
&nbsp;
  // 4. Remove the node from its parent's children array
<span class="cstat-no" title="statement not covered" >  const index = parentNode.children.findIndex(child =&gt; normalizePath(child.path) === normalizedFilePath);</span>
<span class="cstat-no" title="statement not covered" >  if (index &gt; -1) {</span>
<span class="cstat-no" title="statement not covered" >    parentNode.children.splice(index, 1);</span>
<span class="cstat-no" title="statement not covered" >    log(`[removeFileNode] Node removed from parent's children: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >  } else {</span>
<span class="cstat-no" title="statement not covered" >     log(`[removeFileNode] Node not found in parent's children: ${normalizedFilePath}`);</span>
     // Continue removal process anyway, as the node might be detached elsewhere
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // 5. Update the 'dependents' list of files the removed node imported
<span class="cstat-no" title="statement not covered" >  await updateDependentsAfterRemoval(nodeToRemove, activeFileTree); // Pass active tree</span>
&nbsp;
  // 6. Update the 'dependencies' list of files that imported the removed node
<span class="cstat-no" title="statement not covered" >  await updateDependersAfterRemoval(nodeToRemove, activeFileTree); // Pass active tree</span>
&nbsp;
  // 7. Recalculate importance for affected nodes (dependents and dependencies)
<span class="cstat-no" title="statement not covered" >  const affectedPaths = [</span>
<span class="cstat-no" title="statement not covered" >      ...(dependenciesToRemoveFrom ?? []).map(d =&gt; normalizePath(d)),</span>
<span class="cstat-no" title="statement not covered" >      ...(dependentsToUpdate ?? []).map(depPath =&gt; normalizePath(depPath))</span>
<span class="cstat-no" title="statement not covered" >  ];</span>
<span class="cstat-no" title="statement not covered" >  await recalculateImportanceForAffected(affectedPaths, activeFileTree, activeProjectRoot); // Pass active tree &amp; root</span>
&nbsp;
  // 8. Global state update is handled by the caller (mcp-server) after saving
&nbsp;
<span class="cstat-no" title="statement not covered" >  log(`[removeFileNode] Successfully removed node: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
&nbsp;
// --- Helper / Placeholder Functions for Incremental Updates ---
&nbsp;
/**
 * Calculates the importance of a node, considering dependents and dependencies.
 * This adapts the existing `calculateImportance` logic for targeted recalculation.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function calculateNodeImportance(node: FileNode, projectRoot: string): number {</span></span>
   // Use existing initial calculation
<span class="cstat-no" title="statement not covered" >   let importance = calculateInitialImportance(node.path, projectRoot);</span>
&nbsp;
   // Add importance based on number of dependents (files that import this file)
<span class="cstat-no" title="statement not covered" >   const dependentsCount = node.dependents?.length ?? 0;</span>
<span class="cstat-no" title="statement not covered" >   if (dependentsCount &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >       importance += Math.min(dependentsCount, 3);</span>
<span class="cstat-no" title="statement not covered" >   }</span>
&nbsp;
   // Add importance based on number of local dependencies (files this file imports)
<span class="cstat-no" title="statement not covered" >   const localDepsCount = node.dependencies?.length ?? 0;</span>
<span class="cstat-no" title="statement not covered" >   if (localDepsCount &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >       importance += Math.min(localDepsCount, 2);</span>
<span class="cstat-no" title="statement not covered" >   }</span>
&nbsp;
   // Add importance based on number of package dependencies
<span class="cstat-no" title="statement not covered" >   const pkgDeps = node.packageDependencies ?? [];</span>
<span class="cstat-no" title="statement not covered" >   if (pkgDeps.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >       const sdkDeps = pkgDeps.filter(dep =&gt; dep.name?.includes('@modelcontextprotocol/sdk'));</span>
<span class="cstat-no" title="statement not covered" >       const otherDeps = pkgDeps.filter(dep =&gt; !dep.name?.includes('@modelcontextprotocol/sdk'));</span>
<span class="cstat-no" title="statement not covered" >       importance += Math.min(sdkDeps.length, 2); // SDK dependencies are more important</span>
<span class="cstat-no" title="statement not covered" >       importance += Math.min(otherDeps.length, 1); // Other package dependencies</span>
<span class="cstat-no" title="statement not covered" >   }</span>
&nbsp;
   // Cap importance at 10
<span class="cstat-no" title="statement not covered" >   return Math.min(10, Math.max(0, Math.round(importance)));</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates the 'dependents' list of nodes that the new node imports.
 * @param newNode The node that was just added.
 * @param activeFileTree The tree to search within.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function updateDependentsForNewNode(newNode: FileNode, activeFileTree: FileNode): Promise&lt;void&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >   log(`[updateDependentsForNewNode] Updating dependents for new node ${newNode.path}`);</span>
   // Removed reliance on getConfig()
&nbsp;
   // Ensure dependencies is an array
<span class="cstat-no" title="statement not covered" >   for (const depPath of (newNode.dependencies ?? [])) {</span>
<span class="cstat-no" title="statement not covered" >       const depNode = findNodeByPath(activeFileTree, depPath); // depPath is already string</span>
<span class="cstat-no" title="statement not covered" >       if (depNode &amp;&amp; !depNode.isDirectory) {</span>
           // Ensure dependents is an array
<span class="cstat-no" title="statement not covered" >           if (!depNode.dependents) {</span>
<span class="cstat-no" title="statement not covered" >              depNode.dependents = [];</span>
<span class="cstat-no" title="statement not covered" >           }</span>
<span class="cstat-no" title="statement not covered" >           if (!depNode.dependents.includes(newNode.path)) {</span>
<span class="cstat-no" title="statement not covered" >               depNode.dependents.push(newNode.path);</span>
<span class="cstat-no" title="statement not covered" >               log(`[updateDependentsForNewNode] Added ${newNode.path} as dependent for ${depNode.path}`);</span>
<span class="cstat-no" title="statement not covered" >           }</span>
<span class="cstat-no" title="statement not covered" >       } else {</span>
          // console.warn(`[updateDependentsForNewNode] Dependency node not found or is directory: ${depPath}`);
<span class="cstat-no" title="statement not covered" >       }</span>
<span class="cstat-no" title="statement not covered" >   }</span>
   // Package dependencies don't have dependents lists in our model
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates the 'dependents' list of nodes that the removed node imported.
 * @param removedNode The node that was removed.
 * @param activeFileTree The tree to search within.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function updateDependentsAfterRemoval(removedNode: FileNode, activeFileTree: FileNode): Promise&lt;void&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >   log(`[updateDependentsAfterRemoval] Updating dependents after removing ${removedNode.path}`);</span>
    // Removed reliance on getConfig()
&nbsp;
    // Ensure dependencies is an array
<span class="cstat-no" title="statement not covered" >    for (const depPath of (removedNode.dependencies ?? [])) {</span>
<span class="cstat-no" title="statement not covered" >        const depNode = findNodeByPath(activeFileTree, depPath); // depPath is string</span>
<span class="cstat-no" title="statement not covered" >        if (depNode &amp;&amp; !depNode.isDirectory) {</span>
            // Ensure dependents is an array before searching/splicing
<span class="cstat-no" title="statement not covered" >            if (depNode.dependents) {</span>
<span class="cstat-no" title="statement not covered" >                const index = depNode.dependents.indexOf(removedNode.path);</span>
<span class="cstat-no" title="statement not covered" >                if (index &gt; -1) {</span>
<span class="cstat-no" title="statement not covered" >                    depNode.dependents.splice(index, 1);</span>
<span class="cstat-no" title="statement not covered" >                    log(`[updateDependentsAfterRemoval] Removed ${removedNode.path} from dependents of ${depNode.path}`);</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
/**
 * Updates the 'dependencies' list of nodes that imported the removed node.
 * @param removedNode The node that was removed.
 * @param activeFileTree The tree to search within.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function updateDependersAfterRemoval(removedNode: FileNode, activeFileTree: FileNode): Promise&lt;void&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >   log(`[updateDependersAfterRemoval] Updating dependers after removing ${removedNode.path}`);</span>
   // Removed reliance on getConfig()
&nbsp;
   // Ensure dependents is an array
<span class="cstat-no" title="statement not covered" >   for (const dependentPath of (removedNode.dependents ?? [])) {</span>
<span class="cstat-no" title="statement not covered" >       const dependerNode = findNodeByPath(activeFileTree, dependentPath);</span>
<span class="cstat-no" title="statement not covered" >       if (dependerNode &amp;&amp; !dependerNode.isDirectory) {</span>
           // Ensure dependencies is an array before searching/splicing
<span class="cstat-no" title="statement not covered" >           if (dependerNode.dependencies) {</span>
<span class="cstat-no" title="statement not covered" >               const normalizedRemovedPath = normalizePath(removedNode.path);</span>
<span class="cstat-no" title="statement not covered" >               const index = dependerNode.dependencies.findIndex(d =&gt; normalizePath(d) === normalizedRemovedPath);</span>
<span class="cstat-no" title="statement not covered" >               if (index &gt; -1) {</span>
<span class="cstat-no" title="statement not covered" >                   dependerNode.dependencies.splice(index, 1);</span>
<span class="cstat-no" title="statement not covered" >                   log(`[updateDependersAfterRemoval] Removed dependency on ${removedNode.path} from ${dependerNode.path}`);</span>
<span class="cstat-no" title="statement not covered" >               }</span>
<span class="cstat-no" title="statement not covered" >           }</span>
<span class="cstat-no" title="statement not covered" >       }</span>
<span class="cstat-no" title="statement not covered" >   }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
&nbsp;
/**
 * Recalculates importance for a specific set of affected nodes.
 * @param affectedPaths Array of absolute paths for nodes needing recalculation.
 * @param activeFileTree The tree to search/update within.
 * @param activeProjectRoot The project root directory.
 */
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async function recalculateImportanceForAffected(</span></span>
<span class="cstat-no" title="statement not covered" >    affectedPaths: string[],</span>
<span class="cstat-no" title="statement not covered" >    activeFileTree: FileNode,</span>
<span class="cstat-no" title="statement not covered" >    activeProjectRoot: string</span>
<span class="cstat-no" title="statement not covered" >): Promise&lt;void&gt; {</span>
<span class="cstat-no" title="statement not covered" >  log(`[recalculateImportanceForAffected] Recalculating importance for paths: ${JSON.stringify(affectedPaths)}`);</span>
  // Removed reliance on getConfig()
&nbsp;
<span class="cstat-no" title="statement not covered" >  const uniquePaths = [...new Set(affectedPaths)]; // Ensure uniqueness</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  for (const filePath of uniquePaths) {</span>
<span class="cstat-no" title="statement not covered" >    const node = findNodeByPath(activeFileTree, filePath);</span>
<span class="cstat-no" title="statement not covered" >    if (node &amp;&amp; !node.isDirectory) {</span>
<span class="cstat-no" title="statement not covered" >       const oldImportance = node.importance;</span>
       // Use the corrected importance calculation function
<span class="cstat-no" title="statement not covered" >       node.importance = calculateNodeImportance(node, activeProjectRoot);</span>
<span class="cstat-no" title="statement not covered" >       if(oldImportance !== node.importance) {</span>
<span class="cstat-no" title="statement not covered" >          log(`[recalculateImportanceForAffected] Importance for ${node.path} changed from ${oldImportance} to ${node.importance}`);</span>
          // Potential future enhancement: trigger recursive recalculation if importance changed significantly
<span class="cstat-no" title="statement not covered" >       }</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
       // console.warn(`[recalculateImportanceForAffected] Node not found or is directory during recalculation: ${filePath}`);
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
&nbsp;
// --- End of New Functions ---
&nbsp;
/**
 * Recursively calculates importance scores for all file nodes in the tree.
 * Uses calculateNodeImportance for individual node calculation.
 */
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >export async function excludeAndRemoveFile(filePath: string, activeFileTree: FileNode, activeProjectRoot: string): Promise&lt;void&gt; {</span></span>
  // Normalize the file path
<span class="cstat-no" title="statement not covered" >  let absoluteFilePath = filePath;</span>
<span class="cstat-no" title="statement not covered" >  if (!path.isAbsolute(filePath)) {</span>
<span class="cstat-no" title="statement not covered" >    absoluteFilePath = path.join(activeProjectRoot, filePath);</span>
<span class="cstat-no" title="statement not covered" >    log(`[excludeAndRemoveFile] Converted relative path "${filePath}" to absolute path "${absoluteFilePath}"`);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  const normalizedFilePath = normalizePath(absoluteFilePath);</span>
<span class="cstat-no" title="statement not covered" >  log(`[excludeAndRemoveFile] Excluding and removing file: ${normalizedFilePath}`);</span>
&nbsp;
  // Add the file path to the exclusion patterns - use basename pattern to exclude anywhere it appears
<span class="cstat-no" title="statement not covered" >  const basenamePattern = `**/${path.basename(normalizedFilePath)}`;</span>
<span class="cstat-no" title="statement not covered" >  log(`[excludeAndRemoveFile] Adding exclusion pattern: ${basenamePattern}`);</span>
<span class="cstat-no" title="statement not covered" >  addExclusionPattern(basenamePattern);</span>
&nbsp;
  // Remove the file node from the file tree
<span class="cstat-no" title="statement not covered" >  await removeFileNode(normalizedFilePath, activeFileTree, activeProjectRoot);</span>
<span class="cstat-no" title="statement not covered" >  log(`[excludeAndRemoveFile] File removed from tree and added to exclusion patterns: ${normalizedFilePath}`);</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T18:57:32.383Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    