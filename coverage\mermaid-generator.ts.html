
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for mermaid-generator.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> mermaid-generator.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/414</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/414</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { 
  FileNode, 
  MermaidDiagram, 
  MermaidDiagramConfig, 
  MermaidDiagramStyle, 
  MermaidDiagramStats,
  PackageDependency
} from './types.js';
<span class="cstat-no" title="statement not covered" >import path from 'path';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const DEFAULT_STYLE: MermaidDiagramStyle = {</span>
<span class="cstat-no" title="statement not covered" >  nodeColors: {</span>
<span class="cstat-no" title="statement not covered" >    highImportance: '#ff7675',    // Soft red for high importance</span>
<span class="cstat-no" title="statement not covered" >    mediumImportance: '#74b9ff',  // Soft blue for medium importance</span>
<span class="cstat-no" title="statement not covered" >    lowImportance: '#81ecec',     // Soft cyan for low importance</span>
<span class="cstat-no" title="statement not covered" >    package: '#a29bfe',           // Soft purple for packages</span>
<span class="cstat-no" title="statement not covered" >    packageScope: '#ffeaa7'       // Soft yellow for package scopes</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  edgeColors: {</span>
<span class="cstat-no" title="statement not covered" >    dependency: '#636e72',        // Grey for dependencies</span>
<span class="cstat-no" title="statement not covered" >    directory: '#dfe4ea',         // Light grey for directory structure</span>
<span class="cstat-no" title="statement not covered" >    circular: '#e17055',          // Orange for circular dependencies</span>
<span class="cstat-no" title="statement not covered" >    package: '#6c5ce7'            // Purple for package dependencies</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  nodeShapes: {</span>
<span class="cstat-no" title="statement not covered" >    file: 'rect',                 // Rectangle for files</span>
<span class="cstat-no" title="statement not covered" >    directory: 'folder',          // Folder shape for directories</span>
<span class="cstat-no" title="statement not covered" >    important: 'hexagon',         // Hexagon for important files</span>
<span class="cstat-no" title="statement not covered" >    package: 'ellipse',           // Ellipse for packages</span>
<span class="cstat-no" title="statement not covered" >    packageScope: 'stadium'       // Stadium for package scopes</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export class MermaidGenerator {</span>
  private config: MermaidDiagramConfig;
  private fileTree: FileNode;
  private nodes: Map&lt;string, string&gt;; // path -&gt; nodeId
  private nodeInfo: Map&lt;string, { 
    label: string, 
    color: string, 
    shape: string, 
    isDefined: boolean, 
    isPackage: boolean, 
    isPackageScope: boolean,
    childNodes?: string[],
    isCollapsible?: boolean
  }&gt;; // nodeId -&gt; info
  private edges: Map&lt;string, {source: string, target: string, type: string}&gt;; // edgeKey -&gt; edge info
  private edgeCount: number;
  private stats: MermaidDiagramStats;
  private style: MermaidDiagramStyle;
  private definedNodes: Set&lt;string&gt;; // Set of node IDs that have been defined
  private packageScopes: Map&lt;string, Set&lt;string&gt;&gt;; // scope -&gt; set of package names
  private packageScopeNodes: Map&lt;string, string&gt;; // scope -&gt; nodeId
&nbsp;
<span class="cstat-no" title="statement not covered" >  constructor(fileTree: FileNode, config?: Partial&lt;MermaidDiagramConfig&gt;) {</span>
<span class="cstat-no" title="statement not covered" >    this.fileTree = fileTree;</span>
<span class="cstat-no" title="statement not covered" >    this.config = {</span>
<span class="cstat-no" title="statement not covered" >      style: config?.style || 'hybrid',</span>
<span class="cstat-no" title="statement not covered" >      maxDepth: config?.maxDepth || 3,</span>
<span class="cstat-no" title="statement not covered" >      minImportance: config?.minImportance || 0,</span>
<span class="cstat-no" title="statement not covered" >      showDependencies: config?.showDependencies ?? true,</span>
<span class="cstat-no" title="statement not covered" >      showPackageDeps: config?.showPackageDeps ?? false,</span>
<span class="cstat-no" title="statement not covered" >      packageGrouping: config?.packageGrouping ?? true,</span>
<span class="cstat-no" title="statement not covered" >      excludePackages: config?.excludePackages || [],</span>
<span class="cstat-no" title="statement not covered" >      includeOnlyPackages: config?.includeOnlyPackages || [],</span>
<span class="cstat-no" title="statement not covered" >      autoGroupThreshold: config?.autoGroupThreshold || 8,</span>
<span class="cstat-no" title="statement not covered" >      layout: {</span>
<span class="cstat-no" title="statement not covered" >        direction: config?.layout?.direction || 'TB',</span>
<span class="cstat-no" title="statement not covered" >        rankSpacing: config?.layout?.rankSpacing || 50,</span>
<span class="cstat-no" title="statement not covered" >        nodeSpacing: config?.layout?.nodeSpacing || 40</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >    this.style = DEFAULT_STYLE;</span>
<span class="cstat-no" title="statement not covered" >    this.nodes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.nodeInfo = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.edges = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.edgeCount = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.definedNodes = new Set();</span>
<span class="cstat-no" title="statement not covered" >    this.packageScopes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.packageScopeNodes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.stats = {</span>
<span class="cstat-no" title="statement not covered" >      nodeCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      edgeCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      maxDepth: 0,</span>
<span class="cstat-no" title="statement not covered" >      importantFiles: 0,</span>
<span class="cstat-no" title="statement not covered" >      circularDeps: 0,</span>
<span class="cstat-no" title="statement not covered" >      packageCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      packageScopeCount: 0</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // --- Sanitization Helper ---
<span class="cstat-no" title="statement not covered" >  private sanitizeLabel(label: string): string {</span>
<span class="cstat-no" title="statement not covered" >    if (!label || typeof label !== 'string') return '_(empty)';</span>
    // Replace ${...} placeholders FIRST
<span class="cstat-no" title="statement not covered" >    let sanitized = label.replace(/\$\{[^}]+\}/g, '_invalid_import_');</span>
    // Remove potentially problematic characters for Mermaid IDs/labels
    // Allow letters, numbers, underscores, hyphens, periods
    // Replace others with underscore
<span class="cstat-no" title="statement not covered" >    sanitized = sanitized.replace(/[^a-zA-Z0-9_\-\.]/g, '_');</span>
    // Ensure it doesn't start or end with chars invalid for IDs if used as ID
<span class="cstat-no" title="statement not covered" >    sanitized = sanitized.replace(/^[^a-zA-Z0-9_]+|[^a-zA-Z0-9_]+$/g, '');</span>
    // If empty after sanitization, provide a default
<span class="cstat-no" title="statement not covered" >    if (!sanitized) return 'sanitized_empty';</span>
    // Limit length
<span class="cstat-no" title="statement not covered" >    const maxLength = 40; // Increased max length slightly</span>
<span class="cstat-no" title="statement not covered" >    return sanitized.length &lt;= maxLength ? sanitized : sanitized.substring(0, maxLength - 3) + '...';</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  // --- End Sanitization Helper ---
&nbsp;
  // Generate or retrieve a node ID for a given file path
<span class="cstat-no" title="statement not covered" >  private getNodeId(filePath: string, isPackage: boolean = false, isPackageScope: boolean = false): string {</span>
    // If we already have an ID for this path, return it
<span class="cstat-no" title="statement not covered" >    if (this.nodes.has(filePath)) {</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Reusing existing node ID for path: ${filePath} -&gt; ${this.nodes.get(filePath)}`);</span>
<span class="cstat-no" title="statement not covered" >      return this.nodes.get(filePath)!;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Otherwise, generate a new ID and store it
<span class="cstat-no" title="statement not covered" >    const id = `node${this.nodes.size}`;</span>
<span class="cstat-no" title="statement not covered" >    this.nodes.set(filePath, id);</span>
    
    // Initialize basic info for this node (will be refined later if it's a FileNode)
<span class="cstat-no" title="statement not covered" >    const basename = path.basename(filePath);</span>
<span class="cstat-no" title="statement not covered" >    const label = this.sanitizeLabel(basename);</span>
    
    // Set different styling based on node type
<span class="cstat-no" title="statement not covered" >    let color = this.style.nodeColors.lowImportance;</span>
<span class="cstat-no" title="statement not covered" >    let shape = this.style.nodeShapes.file;</span>
    
<span class="cstat-no" title="statement not covered" >    if (isPackage) {</span>
<span class="cstat-no" title="statement not covered" >      color = this.style.nodeColors.package;</span>
<span class="cstat-no" title="statement not covered" >      shape = this.style.nodeShapes.package;</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Created package node ID: ${id} for ${filePath} (${label})`);</span>
<span class="cstat-no" title="statement not covered" >    } else if (isPackageScope) {</span>
<span class="cstat-no" title="statement not covered" >      color = this.style.nodeColors.packageScope;</span>
<span class="cstat-no" title="statement not covered" >      shape = this.style.nodeShapes.packageScope;</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Created package scope node ID: ${id} for ${filePath} (${label})`);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Created file node ID: ${id} for ${filePath} (${label})`);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    this.nodeInfo.set(id, {</span>
<span class="cstat-no" title="statement not covered" >      label,</span>
<span class="cstat-no" title="statement not covered" >      color,</span>
<span class="cstat-no" title="statement not covered" >      shape,</span>
<span class="cstat-no" title="statement not covered" >      isDefined: false, // Not yet defined in output</span>
<span class="cstat-no" title="statement not covered" >      isPackage,</span>
<span class="cstat-no" title="statement not covered" >      isPackageScope</span>
<span class="cstat-no" title="statement not covered" >    });</span>
    
<span class="cstat-no" title="statement not covered" >    return id;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Get or create a package scope node
<span class="cstat-no" title="statement not covered" >  private getPackageScopeNodeId(scope: string): string {</span>
    // Sanitize the scope name before using it as part of the path key
<span class="cstat-no" title="statement not covered" >    const sanitizedScope = this.sanitizeLabel(scope);</span>
<span class="cstat-no" title="statement not covered" >    const scopePathKey = `scope:${sanitizedScope}`;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (this.packageScopeNodes.has(scopePathKey)) {</span>
<span class="cstat-no" title="statement not covered" >      return this.packageScopeNodes.get(scopePathKey)!;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Use the sanitized scope for the node ID path key
<span class="cstat-no" title="statement not covered" >    const nodeId = this.getNodeId(scopePathKey, false, true);</span>
<span class="cstat-no" title="statement not covered" >    this.packageScopeNodes.set(scopePathKey, nodeId);</span>
    
    // Update node info for the scope node
<span class="cstat-no" title="statement not covered" >    const info = this.nodeInfo.get(nodeId)!;</span>
    // Use the sanitized scope for the label as well
<span class="cstat-no" title="statement not covered" >    info.label = sanitizedScope; </span>
<span class="cstat-no" title="statement not covered" >    info.color = this.style.nodeColors.packageScope;</span>
<span class="cstat-no" title="statement not covered" >    info.shape = this.style.nodeShapes.packageScope;</span>
<span class="cstat-no" title="statement not covered" >    info.isPackageScope = true;</span>
    
<span class="cstat-no" title="statement not covered" >    return nodeId;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Update node information based on the actual FileNode
<span class="cstat-no" title="statement not covered" >  private updateNodeInfo(node: FileNode): void {</span>
<span class="cstat-no" title="statement not covered" >    const nodeId = this.getNodeId(node.path);</span>
<span class="cstat-no" title="statement not covered" >    const info = this.nodeInfo.get(nodeId)!;</span>
    
    // Update the label to the proper node label, using sanitization
<span class="cstat-no" title="statement not covered" >    info.label = this.getNodeLabel(node);</span>
    
    // Determine proper color based on importance
<span class="cstat-no" title="statement not covered" >    if (node.isDirectory) {</span>
<span class="cstat-no" title="statement not covered" >      info.color = this.style.nodeColors.mediumImportance;</span>
<span class="cstat-no" title="statement not covered" >      info.shape = this.style.nodeShapes.directory;</span>
<span class="cstat-no" title="statement not covered" >    } else if (node.importance &amp;&amp; node.importance &gt;= 8) {</span>
<span class="cstat-no" title="statement not covered" >      info.color = this.style.nodeColors.highImportance;</span>
<span class="cstat-no" title="statement not covered" >      info.shape = this.style.nodeShapes.important;</span>
<span class="cstat-no" title="statement not covered" >    } else if (node.importance &amp;&amp; node.importance &gt;= 5) {</span>
<span class="cstat-no" title="statement not covered" >      info.color = this.style.nodeColors.mediumImportance;</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      info.color = this.style.nodeColors.lowImportance;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  private getNodeLabel(node: FileNode): string {</span>
    // Use sanitizeLabel
<span class="cstat-no" title="statement not covered" >    return this.sanitizeLabel(node.name);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Create node for a package dependency
<span class="cstat-no" title="statement not covered" >  private addPackageNode(pkg: PackageDependency): string {</span>
    // Ensure pkg.name is a string before processing
<span class="cstat-no" title="statement not covered" >    const packageName = typeof pkg.name === 'string' ? pkg.name : '';</span>
<span class="cstat-no" title="statement not covered" >    const packagePath = typeof pkg.path === 'string' ? pkg.path : ''; // Use path as unique key</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator] Processing package dependency: ${packageName} (${packagePath})`);</span>
&nbsp;
     // Skip if path is missing (likely indicates bad data)
<span class="cstat-no" title="statement not covered" >    if (!packagePath) {</span>
<span class="cstat-no" title="statement not covered" >       console.error(`[MermaidGenerator] Skipping package dependency with missing path.`);</span>
<span class="cstat-no" title="statement not covered" >       return '';</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Skip excluded packages (using sanitized name)
<span class="cstat-no" title="statement not covered" >    const sanitizedPackageName = this.sanitizeLabel(packageName || path.basename(packagePath));</span>
<span class="cstat-no" title="statement not covered" >    if (this.config.excludePackages &amp;&amp; this.config.excludePackages.includes(sanitizedPackageName)) {</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Skipping excluded package: ${sanitizedPackageName}`);</span>
<span class="cstat-no" title="statement not covered" >      return '';</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Only include specific packages if the filter is set (using sanitized name)
<span class="cstat-no" title="statement not covered" >    if (this.config.includeOnlyPackages &amp;&amp; </span>
<span class="cstat-no" title="statement not covered" >        this.config.includeOnlyPackages.length &gt; 0 &amp;&amp; </span>
<span class="cstat-no" title="statement not covered" >        !this.config.includeOnlyPackages.includes(sanitizedPackageName)) {</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Skipping non-included package: ${sanitizedPackageName}`);</span>
<span class="cstat-no" title="statement not covered" >      return '';</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Get or create node ID using package PATH (more unique than name)
<span class="cstat-no" title="statement not covered" >    const nodeId = this.getNodeId(packagePath, true);</span>
    
    // Update node info using the sanitized package NAME
<span class="cstat-no" title="statement not covered" >    const info = this.nodeInfo.get(nodeId)!;</span>
<span class="cstat-no" title="statement not covered" >    info.label = this.getPackageLabel(pkg); // This now uses sanitizeLabel</span>
<span class="cstat-no" title="statement not covered" >    info.color = this.style.nodeColors.package;</span>
<span class="cstat-no" title="statement not covered" >    info.shape = this.style.nodeShapes.package;</span>
<span class="cstat-no" title="statement not covered" >    info.isPackage = true;</span>
    
    // Enhance the label with version if available
<span class="cstat-no" title="statement not covered" >    let label = sanitizedPackageName;</span>
<span class="cstat-no" title="statement not covered" >    if (pkg.version) {</span>
      // Escape caret and parentheses in version strings
<span class="cstat-no" title="statement not covered" >      const escapedVersion = pkg.version.replace(/\^/g, '\\^').replace(/\(/g, '\\(').replace(/\)/g, '\\)');</span>
<span class="cstat-no" title="statement not covered" >      label += ` v${escapedVersion}`;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (pkg.isDevDependency) {</span>
<span class="cstat-no" title="statement not covered" >      label += ' [dev]';</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
    // Escape special characters
<span class="cstat-no" title="statement not covered" >    label = label.replace(/"/g, '\\"').replace(/\[/g, '\\[').replace(/\]/g, '\\]');</span>
<span class="cstat-no" title="statement not covered" >    info.label = label.length &lt;= 30 ? label : label.substring(0, 27) + '...';</span>
    
    // Track package in scope if it has one and grouping is enabled
<span class="cstat-no" title="statement not covered" >    if (pkg.scope &amp;&amp; this.config.packageGrouping) {</span>
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Adding package ${sanitizedPackageName} to scope ${pkg.scope}`);</span>
<span class="cstat-no" title="statement not covered" >      if (!this.packageScopes.has(pkg.scope)) {</span>
<span class="cstat-no" title="statement not covered" >        this.packageScopes.set(pkg.scope, new Set());</span>
<span class="cstat-no" title="statement not covered" >        this.stats.packageScopeCount++;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      this.packageScopes.get(pkg.scope)!.add(sanitizedPackageName);</span>
      
      // Create edge from scope to package
<span class="cstat-no" title="statement not covered" >      const scopeNodeId = this.getPackageScopeNodeId(pkg.scope);</span>
<span class="cstat-no" title="statement not covered" >      this.addEdge(scopeNodeId, nodeId, 'directory');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    this.stats.packageCount++;</span>
<span class="cstat-no" title="statement not covered" >    return nodeId;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Get the label for a package dependency node
<span class="cstat-no" title="statement not covered" >  private getPackageLabel(pkg: PackageDependency): string {</span>
    // Ensure name and path are strings before using them
<span class="cstat-no" title="statement not covered" >    const name = typeof pkg.name === 'string' ? pkg.name : '';</span>
<span class="cstat-no" title="statement not covered" >    const pkgPath = typeof pkg.path === 'string' ? pkg.path : '';</span>
    // Use sanitizeLabel on the package name or fallback to basename of path
<span class="cstat-no" title="statement not covered" >    const labelBase = name || path.basename(pkgPath);</span>
<span class="cstat-no" title="statement not covered" >    return this.sanitizeLabel(labelBase);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // First pass: Collect all nodes and relationships based on style and filters
<span class="cstat-no" title="statement not covered" >  private collectNodesAndEdges(node: FileNode, depth: number = 0): void {</span>
      // --- DEPTH CHECK ---
<span class="cstat-no" title="statement not covered" >      const maxDepth = this.config.maxDepth ?? 5; // Use a default if undefined</span>
<span class="cstat-no" title="statement not covered" >      if (depth &gt;= maxDepth) { // Stop if max depth is reached</span>
<span class="cstat-no" title="statement not covered" >           console.error(`[MermaidGenerator] Max depth ${maxDepth} reached at node: ${node.path}, stopping recursion.`);</span>
<span class="cstat-no" title="statement not covered" >           return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      this.stats.maxDepth = Math.max(this.stats.maxDepth, depth);</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >      const isPackageDepsMode = this.config.style === 'package-deps';</span>
<span class="cstat-no" title="statement not covered" >      const isDirectoryStyle = this.config.style === 'directory';</span>
<span class="cstat-no" title="statement not covered" >      const isDependencyStyle = this.config.style === 'dependency';</span>
<span class="cstat-no" title="statement not covered" >      const isHybridStyle = this.config.style === 'hybrid';</span>
&nbsp;
      // --- NODE FILTERING ---
      // Skip low importance files (unless it's a directory or package-deps mode)
<span class="cstat-no" title="statement not covered" >      if (!node.isDirectory &amp;&amp; !isPackageDepsMode &amp;&amp; this.config.minImportance &amp;&amp; (node.importance || 0) &lt; this.config.minImportance) {</span>
<span class="cstat-no" title="statement not covered" >          console.error(`[MermaidGenerator] Filtering node: Skipping low importance file ${node.path} (${node.importance})`);</span>
<span class="cstat-no" title="statement not covered" >          return; // Skip this node entirely</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Skip directories in dependency-only style
<span class="cstat-no" title="statement not covered" >      if (node.isDirectory &amp;&amp; isDependencyStyle) {</span>
<span class="cstat-no" title="statement not covered" >           console.error(`[MermaidGenerator] Filtering node: Skipping directory ${node.path} in dependency style.`);</span>
           // Still need to process children for their dependencies
<span class="cstat-no" title="statement not covered" >          if (node.children) {</span>
<span class="cstat-no" title="statement not covered" >              for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >                  this.collectNodesAndEdges(child, depth + 1);</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >           return; // Don't register the directory node itself or its containment edges</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // Skip files in directory-only style (unless showDependencies is true)
<span class="cstat-no" title="statement not covered" >      if (!node.isDirectory &amp;&amp; isDirectoryStyle &amp;&amp; !this.config.showDependencies) {</span>
<span class="cstat-no" title="statement not covered" >           console.error(`[MermaidGenerator] Filtering node: Skipping file ${node.path} in directory style.`);</span>
<span class="cstat-no" title="statement not covered" >           return;</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
&nbsp;
      // --- NODE REGISTRATION --- (Get ID and update info, but don't add to definedNodes yet)
<span class="cstat-no" title="statement not covered" >      console.error(`[MermaidGenerator] Registering node: ${node.path} (Depth: ${depth}, Importance: ${node.importance || 'N/A'})`);</span>
<span class="cstat-no" title="statement not covered" >      const nodeId = this.getNodeId(node.path);</span>
<span class="cstat-no" title="statement not covered" >      this.updateNodeInfo(node);</span>
      // Node will be added to definedNodes within addEdge if it connects to anything
&nbsp;
&nbsp;
      // --- RELATIONSHIP PROCESSING ---
&nbsp;
      // 1. Directory Containment Edges (for 'directory' and 'hybrid' styles)
<span class="cstat-no" title="statement not covered" >      if (node.isDirectory &amp;&amp; (isDirectoryStyle || isHybridStyle)) {</span>
<span class="cstat-no" title="statement not covered" >          if (node.children) {</span>
<span class="cstat-no" title="statement not covered" >              for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >                   const childImportance = child.importance || 0;</span>
<span class="cstat-no" title="statement not covered" >                   const childIsDir = child.isDirectory;</span>
<span class="cstat-no" title="statement not covered" >                   const childDepth = depth + 1;</span>
&nbsp;
                   // Conditions for child to be included in diagram:
<span class="cstat-no" title="statement not covered" >                   const passesDepth = childDepth &lt; maxDepth;</span>
<span class="cstat-no" title="statement not covered" >                   const passesImportance = childIsDir || isPackageDepsMode || !this.config.minImportance || childImportance &gt;= this.config.minImportance;</span>
<span class="cstat-no" title="statement not covered" >                   const passesStyleFilter = !(childIsDir &amp;&amp; isDependencyStyle) &amp;&amp; !(!childIsDir &amp;&amp; isDirectoryStyle &amp;&amp; !this.config.showDependencies);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                   if(passesDepth &amp;&amp; passesImportance &amp;&amp; passesStyleFilter) {</span>
<span class="cstat-no" title="statement not covered" >                       const childNodeId = this.getNodeId(child.path); // Ensure child node ID exists in node map</span>
<span class="cstat-no" title="statement not covered" >                       this.addEdge(node.path, child.path, 'directory');</span>
<span class="cstat-no" title="statement not covered" >                   } else {</span>
<span class="cstat-no" title="statement not covered" >                        console.error(`[MermaidGenerator] Skipping directory edge: Child ${child.path} is filtered out (Depth: ${childDepth}, Imp: ${childImportance}, Style: ${this.config.style}).`);</span>
<span class="cstat-no" title="statement not covered" >                   }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
      // 2. Dependency Edges (for 'dependency' and 'hybrid' styles)
<span class="cstat-no" title="statement not covered" >      if (!node.isDirectory &amp;&amp; (isDependencyStyle || isHybridStyle)) {</span>
           // Local file dependencies
<span class="cstat-no" title="statement not covered" >          if (this.config.showDependencies &amp;&amp; node.dependencies) {</span>
<span class="cstat-no" title="statement not covered" >              for (const depPath of node.dependencies) {</span>
                  // addEdge will handle checks if target node exists and is defined
<span class="cstat-no" title="statement not covered" >                  this.addEdge(node.path, depPath, 'dependency');</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
           // Package dependencies
<span class="cstat-no" title="statement not covered" >          if (this.config.showPackageDeps &amp;&amp; node.packageDependencies) {</span>
<span class="cstat-no" title="statement not covered" >              for (const pkgDep of node.packageDependencies) {</span>
<span class="cstat-no" title="statement not covered" >                  if (!pkgDep.name || !pkgDep.path) continue;</span>
<span class="cstat-no" title="statement not covered" >                  const packageNodeId = this.addPackageNode(pkgDep);</span>
<span class="cstat-no" title="statement not covered" >                  if (packageNodeId) {</span>
<span class="cstat-no" title="statement not covered" >                      this.addEdge(node.path, pkgDep.path, 'package');</span>
<span class="cstat-no" title="statement not covered" >                  }</span>
<span class="cstat-no" title="statement not covered" >              }</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
&nbsp;
       // 3. Package Dependency Edges (ONLY for 'package-deps' style)
<span class="cstat-no" title="statement not covered" >       if (!node.isDirectory &amp;&amp; isPackageDepsMode &amp;&amp; node.packageDependencies) {</span>
<span class="cstat-no" title="statement not covered" >           if (this.config.showPackageDeps) {</span>
<span class="cstat-no" title="statement not covered" >                for (const pkgDep of node.packageDependencies) {</span>
<span class="cstat-no" title="statement not covered" >                     if (!pkgDep.name || !pkgDep.path) continue;</span>
<span class="cstat-no" title="statement not covered" >                     const packageNodeId = this.addPackageNode(pkgDep);</span>
<span class="cstat-no" title="statement not covered" >                     if (packageNodeId) {</span>
                          // Ensure the source file node is also marked for definition in this mode
<span class="cstat-no" title="statement not covered" >                          this.definedNodes.add(nodeId);</span>
<span class="cstat-no" title="statement not covered" >                          this.addEdge(node.path, pkgDep.path, 'package');</span>
<span class="cstat-no" title="statement not covered" >                     }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >           }</span>
<span class="cstat-no" title="statement not covered" >       }</span>
&nbsp;
&nbsp;
      // --- RECURSION ---
      // Recurse into children for all styles
      // Filtering happens at the start of the call for the child
<span class="cstat-no" title="statement not covered" >      if (node.isDirectory &amp;&amp; node.children) {</span>
<span class="cstat-no" title="statement not covered" >          for (const child of node.children) {</span>
<span class="cstat-no" title="statement not covered" >               this.collectNodesAndEdges(child, depth + 1);</span>
<span class="cstat-no" title="statement not covered" >          }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Add an edge between two nodes
<span class="cstat-no" title="statement not covered" >  private addEdge(sourcePath: string, targetPath: string, type: string): void {</span>
<span class="cstat-no" title="statement not covered" >    const sourceId = this.getNodeId(sourcePath); // Ensures source node exists in map</span>
<span class="cstat-no" title="statement not covered" >    let targetId;</span>
&nbsp;
    // Ensure target node also exists before adding edge
<span class="cstat-no" title="statement not covered" >    if (type === 'package') {</span>
<span class="cstat-no" title="statement not covered" >        targetId = this.nodes.get(targetPath); // Package nodes use path as key</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >        targetId = this.nodes.get(targetPath); // File/Dir nodes use path as key</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Only add edge if both source and target nodes were actually created/registered
    // Use this.nodes.has() for existence check is slightly better than relying on getNodeInfo
<span class="cstat-no" title="statement not covered" >    if (!targetId || !this.nodes.has(sourcePath) || !this.nodes.has(targetPath)) {</span>
<span class="cstat-no" title="statement not covered" >         console.error(`[MermaidGenerator] Skipping edge from ${sourcePath} to ${targetPath}: Source or Target node path not found in nodes map.`);</span>
<span class="cstat-no" title="statement not covered" >         return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Ensure the nodeInfo map also has entries if needed elsewhere, though nodes map is primary for existence
<span class="cstat-no" title="statement not covered" >    if (!this.nodeInfo.has(sourceId) || !this.nodeInfo.has(targetId)) {</span>
<span class="cstat-no" title="statement not covered" >         console.error(`[MermaidGenerator] Skipping edge from ${sourcePath} to ${targetPath}: Source or Target node info missing.`);</span>
<span class="cstat-no" title="statement not covered" >         return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const edgeKey = `${sourceId}--&gt;${targetId}`; // Use node IDs for edge key</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!this.edges.has(edgeKey)) {</span>
<span class="cstat-no" title="statement not covered" >        console.error(`[MermaidGenerator] Adding edge (${type}): ${sourceId} --&gt; ${targetId}`);</span>
<span class="cstat-no" title="statement not covered" >        this.edges.set(edgeKey, {</span>
<span class="cstat-no" title="statement not covered" >            source: sourceId,</span>
<span class="cstat-no" title="statement not covered" >            target: targetId,</span>
<span class="cstat-no" title="statement not covered" >            type: type</span>
<span class="cstat-no" title="statement not covered" >        });</span>
         // Mark both nodes involved in an edge as defined
<span class="cstat-no" title="statement not covered" >         this.definedNodes.add(sourceId);</span>
<span class="cstat-no" title="statement not covered" >         this.definedNodes.add(targetId);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
    
<span class="cstat-no" title="statement not covered" >  public generate(): MermaidDiagram {</span>
    // Reset state for a clean generation
<span class="cstat-no" title="statement not covered" >    this.nodes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.nodeInfo = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.edges = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.edgeCount = 0;</span>
<span class="cstat-no" title="statement not covered" >    this.definedNodes = new Set(); // Reset defined nodes</span>
<span class="cstat-no" title="statement not covered" >    this.packageScopes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.packageScopeNodes = new Map();</span>
<span class="cstat-no" title="statement not covered" >    this.stats = {</span>
<span class="cstat-no" title="statement not covered" >      nodeCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      edgeCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      maxDepth: 0,</span>
<span class="cstat-no" title="statement not covered" >      importantFiles: 0,</span>
<span class="cstat-no" title="statement not covered" >      circularDeps: 0,</span>
<span class="cstat-no" title="statement not covered" >      packageCount: 0,</span>
<span class="cstat-no" title="statement not covered" >      packageScopeCount: 0</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
    // PHASE 1: Collect all nodes and edges that will be in the diagram
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator] Starting Phase 1: Collecting nodes and edges for style '${this.config.style}'...`);</span>
<span class="cstat-no" title="statement not covered" >    this.collectNodesAndEdges(this.fileTree, 0); // Use the refactored collection function</span>
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator] Finished Phase 1: Collected ${this.nodes.size} potential nodes, ${this.edges.size} potential edges.`);</span>
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator] Nodes marked for definition (involved in edges): ${this.definedNodes.size}`);</span>
    
    // Auto-select layout direction based on diagram structure (optional)
<span class="cstat-no" title="statement not covered" >    let direction = this.config.layout?.direction || 'TB';</span>
    // ... (layout logic can remain or be simplified) ...
    
<span class="cstat-no" title="statement not covered" >    const output: string[] = [</span>
<span class="cstat-no" title="statement not covered" >      `graph ${direction}`</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
&nbsp;
    // Add class definitions
<span class="cstat-no" title="statement not covered" >    output.push(`classDef package-node fill:${this.style.nodeColors.package},stroke:#2d3436,shape:${this.style.nodeShapes.package}`);</span>
<span class="cstat-no" title="statement not covered" >    output.push(`classDef package-scope-node fill:${this.style.nodeColors.packageScope},stroke:#2d3436,shape:${this.style.nodeShapes.packageScope}`);</span>
    // ... (other classDefs)
&nbsp;
    // Add package scope subgraphs if needed
    // ... (subgraph logic can remain the same, relies on collected data) ...
<span class="cstat-no" title="statement not covered" >    if (this.config.packageGrouping) { // Simplified check</span>
<span class="cstat-no" title="statement not covered" >      output.push('');</span>
<span class="cstat-no" title="statement not covered" >      output.push('  %% Package Scopes');</span>
<span class="cstat-no" title="statement not covered" >      for (const [scope, packages] of this.packageScopes.entries()) {</span>
<span class="cstat-no" title="statement not covered" >         const sanitizedScope = this.sanitizeLabel(scope);</span>
<span class="cstat-no" title="statement not covered" >         const scopePathKey = `scope:${sanitizedScope}`;</span>
<span class="cstat-no" title="statement not covered" >         const scopeNodeId = this.packageScopeNodes.get(scopePathKey);</span>
         // Only add subgraph if the scope node itself is needed (i.e., part of an edge)
<span class="cstat-no" title="statement not covered" >         if (scopeNodeId &amp;&amp; this.definedNodes.has(scopeNodeId)) {</span>
<span class="cstat-no" title="statement not covered" >           output.push(`  subgraph ${scopeNodeId}["${sanitizedScope}"]`);</span>
           // Add nodes belonging to this scope that are defined
<span class="cstat-no" title="statement not covered" >           packages.forEach(pkgName =&gt; {</span>
                // Find the node ID for this package name/path more reliably
<span class="cstat-no" title="statement not covered" >                let pkgNodeId: string | undefined;</span>
<span class="cstat-no" title="statement not covered" >                for (const [pathKey, nodeId] of this.nodes.entries()) {</span>
<span class="cstat-no" title="statement not covered" >                    const info = this.nodeInfo.get(nodeId);</span>
<span class="cstat-no" title="statement not covered" >                    if (info?.isPackage &amp;&amp; (this.sanitizeLabel(info.label) === pkgName || path.basename(pathKey) === pkgName)) {</span>
<span class="cstat-no" title="statement not covered" >                        pkgNodeId = nodeId;</span>
<span class="cstat-no" title="statement not covered" >                        break;</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >                if(pkgNodeId &amp;&amp; this.definedNodes.has(pkgNodeId)) {</span>
<span class="cstat-no" title="statement not covered" >                    output.push(`    ${pkgNodeId};`);</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >           });</span>
<span class="cstat-no" title="statement not covered" >           output.push('  end');</span>
<span class="cstat-no" title="statement not covered" >           output.push(`  style ${scopeNodeId} fill:${this.style.nodeColors.packageScope},stroke:#2d3436,stroke-dasharray: 5 5`);</span>
<span class="cstat-no" title="statement not covered" >         }</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >      output.push('');</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // --- Node Definitions and Styles (Phase 2) ---
<span class="cstat-no" title="statement not covered" >    output.push('  %% Node Definitions &amp; Styles');</span>
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator Phase 2] Number of nodes to define (involved in edges): ${this.definedNodes.size}`); </span>
<span class="cstat-no" title="statement not covered" >    let definedCount = 0;</span>
<span class="cstat-no" title="statement not covered" >    for (const [nodeId, info] of this.nodeInfo.entries()) {</span>
       // Only define nodes that are actually marked in the definedNodes set
<span class="cstat-no" title="statement not covered" >       if (this.definedNodes.has(nodeId)) {</span>
<span class="cstat-no" title="statement not covered" >           const labelText = info.label; // Already sanitized</span>
<span class="cstat-no" title="statement not covered" >           output.push(`  ${nodeId}["${labelText}"];`);</span>
           // Apply style
<span class="cstat-no" title="statement not covered" >           output.push(`  style ${nodeId} fill:${info.color},stroke:#333,stroke-width:1px`);</span>
            // Apply class if package/scope
<span class="cstat-no" title="statement not covered" >           if (info.isPackage || info.isPackageScope) {</span>
<span class="cstat-no" title="statement not covered" >               output.push(`  class ${nodeId} ${info.isPackage ? 'package-node' : 'package-scope-node'}`);</span>
<span class="cstat-no" title="statement not covered" >           }</span>
<span class="cstat-no" title="statement not covered" >           definedCount++;</span>
<span class="cstat-no" title="statement not covered" >       }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator Phase 2] Actually defined ${definedCount} nodes.`);</span>
<span class="cstat-no" title="statement not covered" >    output.push('');</span>
&nbsp;
    // --- Edge Definitions (Phase 3) ---
<span class="cstat-no" title="statement not covered" >    output.push('  %% Edge Definitions');</span>
<span class="cstat-no" title="statement not covered" >    const edgeLines: string[] = [];</span>
<span class="cstat-no" title="statement not covered" >    this.edgeCount = 0; // Reset edge count for linkStyle indexing</span>
<span class="cstat-no" title="statement not covered" >    let edgeOutputCount = 0;</span>
<span class="cstat-no" title="statement not covered" >    for (const edge of this.edges.values()) {</span>
      // Double-check both nodes exist in definedNodes before creating edge link
<span class="cstat-no" title="statement not covered" >      if (this.definedNodes.has(edge.source) &amp;&amp; this.definedNodes.has(edge.target)) {</span>
<span class="cstat-no" title="statement not covered" >        edgeLines.push(`  ${edge.source} --&gt; ${edge.target}`);</span>
        
        // Choose edge color/style based on type
<span class="cstat-no" title="statement not covered" >        let color = this.style.edgeColors.dependency; // Default</span>
<span class="cstat-no" title="statement not covered" >        let strokeWidth = '1px';</span>
<span class="cstat-no" title="statement not covered" >        let strokeDasharray = 'solid'; // Default</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        switch (edge.type) {</span>
<span class="cstat-no" title="statement not covered" >            case 'directory':</span>
<span class="cstat-no" title="statement not covered" >                color = this.style.edgeColors.directory;</span>
<span class="cstat-no" title="statement not covered" >                strokeWidth = '2px';</span>
<span class="cstat-no" title="statement not covered" >                strokeDasharray = '5,5'; // Dashed line for directory containment</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
<span class="cstat-no" title="statement not covered" >            case 'package':</span>
<span class="cstat-no" title="statement not covered" >                color = this.style.edgeColors.package;</span>
<span class="cstat-no" title="statement not covered" >                strokeWidth = '1.5px';</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
<span class="cstat-no" title="statement not covered" >            case 'dependency':</span>
<span class="cstat-no" title="statement not covered" >                color = this.style.edgeColors.dependency;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            // Add case for 'circular' if detected
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        edgeLines.push(`  linkStyle ${this.edgeCount} stroke:${color},stroke-width:${strokeWidth}${strokeDasharray !== 'solid' ? `,stroke-dasharray:${strokeDasharray}` : ''}`);</span>
<span class="cstat-no" title="statement not covered" >        this.edgeCount++; // Increment index for linkStyle</span>
<span class="cstat-no" title="statement not covered" >        edgeOutputCount++;</span>
<span class="cstat-no" title="statement not covered" >        this.stats.edgeCount++;</span>
         // Add logic to update other stats like circularDeps, importantFiles if needed
<span class="cstat-no" title="statement not covered" >      } else {</span>
<span class="cstat-no" title="statement not covered" >          console.error(`[MermaidGenerator Phase 3] Skipping edge ${edge.source} --&gt; ${edge.target} because one or both nodes were not defined.`);</span>
<span class="cstat-no" title="statement not covered" >      }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    console.error(`[MermaidGenerator Phase 3] Added ${edgeOutputCount} edges to the output.`);</span>
<span class="cstat-no" title="statement not covered" >    output.push(...edgeLines);</span>
&nbsp;
    // Update stats
<span class="cstat-no" title="statement not covered" >    this.stats.nodeCount = this.definedNodes.size;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return {</span>
<span class="cstat-no" title="statement not covered" >      code: output.join('\n'),</span>
<span class="cstat-no" title="statement not covered" >      style: this.style,</span>
<span class="cstat-no" title="statement not covered" >      stats: this.stats,</span>
<span class="cstat-no" title="statement not covered" >      timestamp: new Date()</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >} </span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-10T18:57:32.383Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    