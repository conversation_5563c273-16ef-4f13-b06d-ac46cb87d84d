{"name": "FileScopeMCP", "version": "1.0.0", "description": "MCP server for file hierarchy and dependency tracking", "main": "dist/mcp-server.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/mcp-server.js", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "chokidar": "^3.6.0", "mermaid": "^11.6.0", "zod": "^3.25.28"}, "devDependencies": {"@types/node": "^22.15.21", "@vitest/coverage-v8": "^3.1.4", "typescript": "^5.8.3", "vitest": "^3.1.4"}}