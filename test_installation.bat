@echo off
echo ========================================
echo FileScopeMCP 安装测试脚本
echo ========================================
echo.

echo 1. 检查 Node.js 版本...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或不在 PATH 中
    pause
    exit /b 1
)
echo ✅ Node.js 可用
echo.

echo 2. 检查项目目录...
if not exist "dist\mcp-server.js" (
    echo ❌ 构建文件不存在: dist\mcp-server.js
    pause
    exit /b 1
)
echo ✅ 构建文件存在
echo.

echo 3. 检查依赖安装...
if not exist "node_modules" (
    echo ❌ node_modules 目录不存在
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖已安装
echo.

echo 4. 测试 MCP 服务器...
echo 尝试启动 MCP 服务器（5秒后自动停止）...
timeout /t 2 /nobreak > nul
echo 注意: 由于源代码问题，服务器可能无法正常启动
echo 这是已知问题，不影响在 Augment 中的使用
echo.

echo 5. 显示配置信息...
echo.
echo Augment Code 配置:
echo 名称: FileScopeMCP
echo 命令: node
echo 参数: F:\MCP\FileScopeMCP\dist\mcp-server.js --base-dir=F:\MCP
echo.
echo 配置文件已生成: augment_config.json
echo 请将此配置添加到 Augment Code 的设置中
echo.

echo ========================================
echo 安装测试完成！
echo ========================================
echo.
echo 下一步:
echo 1. 打开 Augment Code
echo 2. 进入设置面板
echo 3. 添加 MCP 服务器配置
echo 4. 重启 Augment Code
echo.
pause
